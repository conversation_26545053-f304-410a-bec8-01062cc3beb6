(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))s(f);new MutationObserver(f=>{for(const d of f)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&s(m)}).observe(document,{childList:!0,subtree:!0});function i(f){const d={};return f.integrity&&(d.integrity=f.integrity),f.referrerPolicy&&(d.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?d.credentials="include":f.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(f){if(f.ep)return;f.ep=!0;const d=i(f);fetch(f.href,d)}})();var xr={exports:{}},nu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pm;function $_(){if(pm)return nu;pm=1;var r=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function i(s,f,d){var m=null;if(d!==void 0&&(m=""+d),f.key!==void 0&&(m=""+f.key),"key"in f){d={};for(var b in f)b!=="key"&&(d[b]=f[b])}else d=f;return f=d.ref,{$$typeof:r,type:s,key:m,ref:f!==void 0?f:null,props:d}}return nu.Fragment=l,nu.jsx=i,nu.jsxs=i,nu}var bm;function K_(){return bm||(bm=1,xr.exports=$_()),xr.exports}var L=K_(),Mr={exports:{}},st={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sm;function J_(){if(Sm)return st;Sm=1;var r=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),m=Symbol.for("react.context"),b=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),M=Symbol.iterator;function U(g){return g===null||typeof g!="object"?null:(g=M&&g[M]||g["@@iterator"],typeof g=="function"?g:null)}var C={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},q=Object.assign,Q={};function Z(g,B,X){this.props=g,this.context=B,this.refs=Q,this.updater=X||C}Z.prototype.isReactComponent={},Z.prototype.setState=function(g,B){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,B,"setState")},Z.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function Et(){}Et.prototype=Z.prototype;function $(g,B,X){this.props=g,this.context=B,this.refs=Q,this.updater=X||C}var J=$.prototype=new Et;J.constructor=$,q(J,Z.prototype),J.isPureReactComponent=!0;var V=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},ct=Object.prototype.hasOwnProperty;function tt(g,B,X,j,F,_t){return X=_t.ref,{$$typeof:r,type:g,key:B,ref:X!==void 0?X:null,props:_t}}function Rt(g,B){return tt(g.type,B,void 0,void 0,void 0,g.props)}function mt(g){return typeof g=="object"&&g!==null&&g.$$typeof===r}function bt(g){var B={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(X){return B[X]})}var ot=/\/+/g;function zt(g,B){return typeof g=="object"&&g!==null&&g.key!=null?bt(""+g.key):B.toString(36)}function je(){}function Ie(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(je,je):(g.status="pending",g.then(function(B){g.status==="pending"&&(g.status="fulfilled",g.value=B)},function(B){g.status==="pending"&&(g.status="rejected",g.reason=B)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function Gt(g,B,X,j,F){var _t=typeof g;(_t==="undefined"||_t==="boolean")&&(g=null);var it=!1;if(g===null)it=!0;else switch(_t){case"bigint":case"string":case"number":it=!0;break;case"object":switch(g.$$typeof){case r:case l:it=!0;break;case R:return it=g._init,Gt(it(g._payload),B,X,j,F)}}if(it)return F=F(g),it=j===""?"."+zt(g,0):j,V(F)?(X="",it!=null&&(X=it.replace(ot,"$&/")+"/"),Gt(F,B,X,"",function(_a){return _a})):F!=null&&(mt(F)&&(F=Rt(F,X+(F.key==null||g&&g.key===F.key?"":(""+F.key).replace(ot,"$&/")+"/")+it)),B.push(F)),1;it=0;var ye=j===""?".":j+":";if(V(g))for(var Dt=0;Dt<g.length;Dt++)j=g[Dt],_t=ye+zt(j,Dt),it+=Gt(j,B,X,_t,F);else if(Dt=U(g),typeof Dt=="function")for(g=Dt.call(g),Dt=0;!(j=g.next()).done;)j=j.value,_t=ye+zt(j,Dt++),it+=Gt(j,B,X,_t,F);else if(_t==="object"){if(typeof g.then=="function")return Gt(Ie(g),B,X,j,F);throw B=String(g),Error("Objects are not valid as a React child (found: "+(B==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":B)+"). If you meant to render a collection of children, use an array instead.")}return it}function N(g,B,X){if(g==null)return g;var j=[],F=0;return Gt(g,j,"","",function(_t){return B.call(X,_t,F++)}),j}function Y(g){if(g._status===-1){var B=g._result;B=B(),B.then(function(X){(g._status===0||g._status===-1)&&(g._status=1,g._result=X)},function(X){(g._status===0||g._status===-1)&&(g._status=2,g._result=X)}),g._status===-1&&(g._status=0,g._result=B)}if(g._status===1)return g._result.default;throw g._result}var et=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var B=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(B))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function xt(){}return st.Children={map:N,forEach:function(g,B,X){N(g,function(){B.apply(this,arguments)},X)},count:function(g){var B=0;return N(g,function(){B++}),B},toArray:function(g){return N(g,function(B){return B})||[]},only:function(g){if(!mt(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},st.Component=Z,st.Fragment=i,st.Profiler=f,st.PureComponent=$,st.StrictMode=s,st.Suspense=A,st.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,st.__COMPILER_RUNTIME={__proto__:null,c:function(g){return k.H.useMemoCache(g)}},st.cache=function(g){return function(){return g.apply(null,arguments)}},st.cloneElement=function(g,B,X){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var j=q({},g.props),F=g.key,_t=void 0;if(B!=null)for(it in B.ref!==void 0&&(_t=void 0),B.key!==void 0&&(F=""+B.key),B)!ct.call(B,it)||it==="key"||it==="__self"||it==="__source"||it==="ref"&&B.ref===void 0||(j[it]=B[it]);var it=arguments.length-2;if(it===1)j.children=X;else if(1<it){for(var ye=Array(it),Dt=0;Dt<it;Dt++)ye[Dt]=arguments[Dt+2];j.children=ye}return tt(g.type,F,void 0,void 0,_t,j)},st.createContext=function(g){return g={$$typeof:m,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:d,_context:g},g},st.createElement=function(g,B,X){var j,F={},_t=null;if(B!=null)for(j in B.key!==void 0&&(_t=""+B.key),B)ct.call(B,j)&&j!=="key"&&j!=="__self"&&j!=="__source"&&(F[j]=B[j]);var it=arguments.length-2;if(it===1)F.children=X;else if(1<it){for(var ye=Array(it),Dt=0;Dt<it;Dt++)ye[Dt]=arguments[Dt+2];F.children=ye}if(g&&g.defaultProps)for(j in it=g.defaultProps,it)F[j]===void 0&&(F[j]=it[j]);return tt(g,_t,void 0,void 0,null,F)},st.createRef=function(){return{current:null}},st.forwardRef=function(g){return{$$typeof:b,render:g}},st.isValidElement=mt,st.lazy=function(g){return{$$typeof:R,_payload:{_status:-1,_result:g},_init:Y}},st.memo=function(g,B){return{$$typeof:v,type:g,compare:B===void 0?null:B}},st.startTransition=function(g){var B=k.T,X={};k.T=X;try{var j=g(),F=k.S;F!==null&&F(X,j),typeof j=="object"&&j!==null&&typeof j.then=="function"&&j.then(xt,et)}catch(_t){et(_t)}finally{k.T=B}},st.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},st.use=function(g){return k.H.use(g)},st.useActionState=function(g,B,X){return k.H.useActionState(g,B,X)},st.useCallback=function(g,B){return k.H.useCallback(g,B)},st.useContext=function(g){return k.H.useContext(g)},st.useDebugValue=function(){},st.useDeferredValue=function(g,B){return k.H.useDeferredValue(g,B)},st.useEffect=function(g,B,X){var j=k.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return j.useEffect(g,B)},st.useId=function(){return k.H.useId()},st.useImperativeHandle=function(g,B,X){return k.H.useImperativeHandle(g,B,X)},st.useInsertionEffect=function(g,B){return k.H.useInsertionEffect(g,B)},st.useLayoutEffect=function(g,B){return k.H.useLayoutEffect(g,B)},st.useMemo=function(g,B){return k.H.useMemo(g,B)},st.useOptimistic=function(g,B){return k.H.useOptimistic(g,B)},st.useReducer=function(g,B,X){return k.H.useReducer(g,B,X)},st.useRef=function(g){return k.H.useRef(g)},st.useState=function(g){return k.H.useState(g)},st.useSyncExternalStore=function(g,B,X){return k.H.useSyncExternalStore(g,B,X)},st.useTransition=function(){return k.H.useTransition()},st.version="19.1.0",st}var Am;function nf(){return Am||(Am=1,Mr.exports=J_()),Mr.exports}var ga=nf(),Nr={exports:{}},lu={},Dr={exports:{}},Cr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Em;function k_(){return Em||(Em=1,function(r){function l(N,Y){var et=N.length;N.push(Y);t:for(;0<et;){var xt=et-1>>>1,g=N[xt];if(0<f(g,Y))N[xt]=Y,N[et]=g,et=xt;else break t}}function i(N){return N.length===0?null:N[0]}function s(N){if(N.length===0)return null;var Y=N[0],et=N.pop();if(et!==Y){N[0]=et;t:for(var xt=0,g=N.length,B=g>>>1;xt<B;){var X=2*(xt+1)-1,j=N[X],F=X+1,_t=N[F];if(0>f(j,et))F<g&&0>f(_t,j)?(N[xt]=_t,N[F]=et,xt=F):(N[xt]=j,N[X]=et,xt=X);else if(F<g&&0>f(_t,et))N[xt]=_t,N[F]=et,xt=F;else break t}}return Y}function f(N,Y){var et=N.sortIndex-Y.sortIndex;return et!==0?et:N.id-Y.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;r.unstable_now=function(){return d.now()}}else{var m=Date,b=m.now();r.unstable_now=function(){return m.now()-b}}var A=[],v=[],R=1,M=null,U=3,C=!1,q=!1,Q=!1,Z=!1,Et=typeof setTimeout=="function"?setTimeout:null,$=typeof clearTimeout=="function"?clearTimeout:null,J=typeof setImmediate<"u"?setImmediate:null;function V(N){for(var Y=i(v);Y!==null;){if(Y.callback===null)s(v);else if(Y.startTime<=N)s(v),Y.sortIndex=Y.expirationTime,l(A,Y);else break;Y=i(v)}}function k(N){if(Q=!1,V(N),!q)if(i(A)!==null)q=!0,ct||(ct=!0,zt());else{var Y=i(v);Y!==null&&Gt(k,Y.startTime-N)}}var ct=!1,tt=-1,Rt=5,mt=-1;function bt(){return Z?!0:!(r.unstable_now()-mt<Rt)}function ot(){if(Z=!1,ct){var N=r.unstable_now();mt=N;var Y=!0;try{t:{q=!1,Q&&(Q=!1,$(tt),tt=-1),C=!0;var et=U;try{e:{for(V(N),M=i(A);M!==null&&!(M.expirationTime>N&&bt());){var xt=M.callback;if(typeof xt=="function"){M.callback=null,U=M.priorityLevel;var g=xt(M.expirationTime<=N);if(N=r.unstable_now(),typeof g=="function"){M.callback=g,V(N),Y=!0;break e}M===i(A)&&s(A),V(N)}else s(A);M=i(A)}if(M!==null)Y=!0;else{var B=i(v);B!==null&&Gt(k,B.startTime-N),Y=!1}}break t}finally{M=null,U=et,C=!1}Y=void 0}}finally{Y?zt():ct=!1}}}var zt;if(typeof J=="function")zt=function(){J(ot)};else if(typeof MessageChannel<"u"){var je=new MessageChannel,Ie=je.port2;je.port1.onmessage=ot,zt=function(){Ie.postMessage(null)}}else zt=function(){Et(ot,0)};function Gt(N,Y){tt=Et(function(){N(r.unstable_now())},Y)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(N){N.callback=null},r.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Rt=0<N?Math.floor(1e3/N):5},r.unstable_getCurrentPriorityLevel=function(){return U},r.unstable_next=function(N){switch(U){case 1:case 2:case 3:var Y=3;break;default:Y=U}var et=U;U=Y;try{return N()}finally{U=et}},r.unstable_requestPaint=function(){Z=!0},r.unstable_runWithPriority=function(N,Y){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var et=U;U=N;try{return Y()}finally{U=et}},r.unstable_scheduleCallback=function(N,Y,et){var xt=r.unstable_now();switch(typeof et=="object"&&et!==null?(et=et.delay,et=typeof et=="number"&&0<et?xt+et:xt):et=xt,N){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=et+g,N={id:R++,callback:Y,priorityLevel:N,startTime:et,expirationTime:g,sortIndex:-1},et>xt?(N.sortIndex=et,l(v,N),i(A)===null&&N===i(v)&&(Q?($(tt),tt=-1):Q=!0,Gt(k,et-xt))):(N.sortIndex=g,l(A,N),q||C||(q=!0,ct||(ct=!0,zt()))),N},r.unstable_shouldYield=bt,r.unstable_wrapCallback=function(N){var Y=U;return function(){var et=U;U=Y;try{return N.apply(this,arguments)}finally{U=et}}}}(Cr)),Cr}var wm;function W_(){return wm||(wm=1,Dr.exports=k_()),Dr.exports}var zr={exports:{}},ue={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tm;function F_(){if(Tm)return ue;Tm=1;var r=nf();function l(A){var v="https://react.dev/errors/"+A;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var R=2;R<arguments.length;R++)v+="&args[]="+encodeURIComponent(arguments[R])}return"Minified React error #"+A+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var s={d:{f:i,r:function(){throw Error(l(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},f=Symbol.for("react.portal");function d(A,v,R){var M=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:M==null?null:""+M,children:A,containerInfo:v,implementation:R}}var m=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function b(A,v){if(A==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return ue.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,ue.createPortal=function(A,v){var R=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(l(299));return d(A,v,null,R)},ue.flushSync=function(A){var v=m.T,R=s.p;try{if(m.T=null,s.p=2,A)return A()}finally{m.T=v,s.p=R,s.d.f()}},ue.preconnect=function(A,v){typeof A=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,s.d.C(A,v))},ue.prefetchDNS=function(A){typeof A=="string"&&s.d.D(A)},ue.preinit=function(A,v){if(typeof A=="string"&&v&&typeof v.as=="string"){var R=v.as,M=b(R,v.crossOrigin),U=typeof v.integrity=="string"?v.integrity:void 0,C=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;R==="style"?s.d.S(A,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:M,integrity:U,fetchPriority:C}):R==="script"&&s.d.X(A,{crossOrigin:M,integrity:U,fetchPriority:C,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},ue.preinitModule=function(A,v){if(typeof A=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var R=b(v.as,v.crossOrigin);s.d.M(A,{crossOrigin:R,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&s.d.M(A)},ue.preload=function(A,v){if(typeof A=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var R=v.as,M=b(R,v.crossOrigin);s.d.L(A,R,{crossOrigin:M,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},ue.preloadModule=function(A,v){if(typeof A=="string")if(v){var R=b(v.as,v.crossOrigin);s.d.m(A,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:R,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else s.d.m(A)},ue.requestFormReset=function(A){s.d.r(A)},ue.unstable_batchedUpdates=function(A,v){return A(v)},ue.useFormState=function(A,v,R){return m.H.useFormState(A,v,R)},ue.useFormStatus=function(){return m.H.useHostTransitionStatus()},ue.version="19.1.0",ue}var Om;function I_(){if(Om)return zr.exports;Om=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(l){console.error(l)}}return r(),zr.exports=F_(),zr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rm;function P_(){if(Rm)return lu;Rm=1;var r=W_(),l=nf(),i=I_();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function m(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function b(t){if(d(t)!==t)throw Error(s(188))}function A(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(s(188));return e!==t?null:t}for(var a=t,n=e;;){var u=a.return;if(u===null)break;var c=u.alternate;if(c===null){if(n=u.return,n!==null){a=n;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===a)return b(u),t;if(c===n)return b(u),e;c=c.sibling}throw Error(s(188))}if(a.return!==n.return)a=u,n=c;else{for(var o=!1,h=u.child;h;){if(h===a){o=!0,a=u,n=c;break}if(h===n){o=!0,n=u,a=c;break}h=h.sibling}if(!o){for(h=c.child;h;){if(h===a){o=!0,a=c,n=u;break}if(h===n){o=!0,n=c,a=u;break}h=h.sibling}if(!o)throw Error(s(189))}}if(a.alternate!==n)throw Error(s(190))}if(a.tag!==3)throw Error(s(188));return a.stateNode.current===a?t:e}function v(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=v(t),e!==null)return e;t=t.sibling}return null}var R=Object.assign,M=Symbol.for("react.element"),U=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),q=Symbol.for("react.fragment"),Q=Symbol.for("react.strict_mode"),Z=Symbol.for("react.profiler"),Et=Symbol.for("react.provider"),$=Symbol.for("react.consumer"),J=Symbol.for("react.context"),V=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),ct=Symbol.for("react.suspense_list"),tt=Symbol.for("react.memo"),Rt=Symbol.for("react.lazy"),mt=Symbol.for("react.activity"),bt=Symbol.for("react.memo_cache_sentinel"),ot=Symbol.iterator;function zt(t){return t===null||typeof t!="object"?null:(t=ot&&t[ot]||t["@@iterator"],typeof t=="function"?t:null)}var je=Symbol.for("react.client.reference");function Ie(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===je?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case q:return"Fragment";case Z:return"Profiler";case Q:return"StrictMode";case k:return"Suspense";case ct:return"SuspenseList";case mt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case C:return"Portal";case J:return(t.displayName||"Context")+".Provider";case $:return(t._context.displayName||"Context")+".Consumer";case V:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case tt:return e=t.displayName||null,e!==null?e:Ie(t.type)||"Memo";case Rt:e=t._payload,t=t._init;try{return Ie(t(e))}catch{}}return null}var Gt=Array.isArray,N=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,et={pending:!1,data:null,method:null,action:null},xt=[],g=-1;function B(t){return{current:t}}function X(t){0>g||(t.current=xt[g],xt[g]=null,g--)}function j(t,e){g++,xt[g]=t.current,t.current=e}var F=B(null),_t=B(null),it=B(null),ye=B(null);function Dt(t,e){switch(j(it,e),j(_t,t),j(F,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Kh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Kh(e),t=Jh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}X(F),j(F,t)}function _a(){X(F),X(_t),X(it)}function hs(t){t.memoizedState!==null&&j(ye,t);var e=F.current,a=Jh(e,t.type);e!==a&&(j(_t,t),j(F,a))}function Au(t){_t.current===t&&(X(F),X(_t)),ye.current===t&&(X(ye),Il._currentValue=et)}var ms=Object.prototype.hasOwnProperty,ys=r.unstable_scheduleCallback,gs=r.unstable_cancelCallback,wg=r.unstable_shouldYield,Tg=r.unstable_requestPaint,Qe=r.unstable_now,Og=r.unstable_getCurrentPriorityLevel,Of=r.unstable_ImmediatePriority,Rf=r.unstable_UserBlockingPriority,Eu=r.unstable_NormalPriority,Rg=r.unstable_LowPriority,xf=r.unstable_IdlePriority,xg=r.log,Mg=r.unstable_setDisableYieldValue,il=null,ge=null;function va(t){if(typeof xg=="function"&&Mg(t),ge&&typeof ge.setStrictMode=="function")try{ge.setStrictMode(il,t)}catch{}}var _e=Math.clz32?Math.clz32:Cg,Ng=Math.log,Dg=Math.LN2;function Cg(t){return t>>>=0,t===0?32:31-(Ng(t)/Dg|0)|0}var wu=256,Tu=4194304;function Za(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ou(t,e,a){var n=t.pendingLanes;if(n===0)return 0;var u=0,c=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var h=n&134217727;return h!==0?(n=h&~c,n!==0?u=Za(n):(o&=h,o!==0?u=Za(o):a||(a=h&~t,a!==0&&(u=Za(a))))):(h=n&~c,h!==0?u=Za(h):o!==0?u=Za(o):a||(a=n&~t,a!==0&&(u=Za(a)))),u===0?0:e!==0&&e!==u&&(e&c)===0&&(c=u&-u,a=e&-e,c>=a||c===32&&(a&4194048)!==0)?e:u}function sl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function zg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Mf(){var t=wu;return wu<<=1,(wu&4194048)===0&&(wu=256),t}function Nf(){var t=Tu;return Tu<<=1,(Tu&62914560)===0&&(Tu=4194304),t}function _s(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function cl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Ug(t,e,a,n,u,c){var o=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var h=t.entanglements,y=t.expirationTimes,E=t.hiddenUpdates;for(a=o&~a;0<a;){var x=31-_e(a),z=1<<x;h[x]=0,y[x]=-1;var w=E[x];if(w!==null)for(E[x]=null,x=0;x<w.length;x++){var T=w[x];T!==null&&(T.lane&=-536870913)}a&=~z}n!==0&&Df(t,n,0),c!==0&&u===0&&t.tag!==0&&(t.suspendedLanes|=c&~(o&~e))}function Df(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-_e(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|a&4194090}function Cf(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var n=31-_e(a),u=1<<n;u&e|t[n]&e&&(t[n]|=e),a&=~u}}function vs(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ps(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function zf(){var t=Y.p;return t!==0?t:(t=window.event,t===void 0?32:hm(t.type))}function Bg(t,e){var a=Y.p;try{return Y.p=t,e()}finally{Y.p=a}}var pa=Math.random().toString(36).slice(2),ne="__reactFiber$"+pa,re="__reactProps$"+pa,yn="__reactContainer$"+pa,bs="__reactEvents$"+pa,Hg="__reactListeners$"+pa,qg="__reactHandles$"+pa,Uf="__reactResources$"+pa,rl="__reactMarker$"+pa;function Ss(t){delete t[ne],delete t[re],delete t[bs],delete t[Hg],delete t[qg]}function gn(t){var e=t[ne];if(e)return e;for(var a=t.parentNode;a;){if(e=a[yn]||a[ne]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=Ih(t);t!==null;){if(a=t[ne])return a;t=Ih(t)}return e}t=a,a=t.parentNode}return null}function _n(t){if(t=t[ne]||t[yn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function vn(t){var e=t[Uf];return e||(e=t[Uf]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Kt(t){t[rl]=!0}var Bf=new Set,Hf={};function Va(t,e){pn(t,e),pn(t+"Capture",e)}function pn(t,e){for(Hf[t]=e,t=0;t<e.length;t++)Bf.add(e[t])}var jg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),qf={},jf={};function Lg(t){return ms.call(jf,t)?!0:ms.call(qf,t)?!1:jg.test(t)?jf[t]=!0:(qf[t]=!0,!1)}function Ru(t,e,a){if(Lg(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function xu(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function Pe(t,e,a,n){if(n===null)t.removeAttribute(a);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+n)}}var As,Lf;function bn(t){if(As===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);As=e&&e[1]||"",Lf=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+As+t+Lf}var Es=!1;function ws(t,e){if(!t||Es)return"";Es=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(T){var w=T}Reflect.construct(t,[],z)}else{try{z.call()}catch(T){w=T}t.call(z.prototype)}}else{try{throw Error()}catch(T){w=T}(z=t())&&typeof z.catch=="function"&&z.catch(function(){})}}catch(T){if(T&&w&&typeof T.stack=="string")return[T.stack,w.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=n.DetermineComponentFrameRoot(),o=c[0],h=c[1];if(o&&h){var y=o.split(`
`),E=h.split(`
`);for(u=n=0;n<y.length&&!y[n].includes("DetermineComponentFrameRoot");)n++;for(;u<E.length&&!E[u].includes("DetermineComponentFrameRoot");)u++;if(n===y.length||u===E.length)for(n=y.length-1,u=E.length-1;1<=n&&0<=u&&y[n]!==E[u];)u--;for(;1<=n&&0<=u;n--,u--)if(y[n]!==E[u]){if(n!==1||u!==1)do if(n--,u--,0>u||y[n]!==E[u]){var x=`
`+y[n].replace(" at new "," at ");return t.displayName&&x.includes("<anonymous>")&&(x=x.replace("<anonymous>",t.displayName)),x}while(1<=n&&0<=u);break}}}finally{Es=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?bn(a):""}function Yg(t){switch(t.tag){case 26:case 27:case 5:return bn(t.type);case 16:return bn("Lazy");case 13:return bn("Suspense");case 19:return bn("SuspenseList");case 0:case 15:return ws(t.type,!1);case 11:return ws(t.type.render,!1);case 1:return ws(t.type,!0);case 31:return bn("Activity");default:return""}}function Yf(t){try{var e="";do e+=Yg(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function xe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Xf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Xg(t){var e=Xf(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),n=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var u=a.get,c=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return u.call(this)},set:function(o){n=""+o,c.call(this,o)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return n},setValue:function(o){n=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Mu(t){t._valueTracker||(t._valueTracker=Xg(t))}function Gf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),n="";return t&&(n=Xf(t)?t.checked?"true":"false":t.value),t=n,t!==a?(e.setValue(t),!0):!1}function Nu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Gg=/[\n"\\]/g;function Me(t){return t.replace(Gg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ts(t,e,a,n,u,c,o,h){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+xe(e)):t.value!==""+xe(e)&&(t.value=""+xe(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?Os(t,o,xe(e)):a!=null?Os(t,o,xe(a)):n!=null&&t.removeAttribute("value"),u==null&&c!=null&&(t.defaultChecked=!!c),u!=null&&(t.checked=u&&typeof u!="function"&&typeof u!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+xe(h):t.removeAttribute("name")}function Qf(t,e,a,n,u,c,o,h){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;a=a!=null?""+xe(a):"",e=e!=null?""+xe(e):a,h||e===t.value||(t.value=e),t.defaultValue=e}n=n??u,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=h?t.checked:!!n,t.defaultChecked=!!n,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function Os(t,e,a){e==="number"&&Nu(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function Sn(t,e,a,n){if(t=t.options,e){e={};for(var u=0;u<a.length;u++)e["$"+a[u]]=!0;for(a=0;a<t.length;a++)u=e.hasOwnProperty("$"+t[a].value),t[a].selected!==u&&(t[a].selected=u),u&&n&&(t[a].defaultSelected=!0)}else{for(a=""+xe(a),e=null,u=0;u<t.length;u++){if(t[u].value===a){t[u].selected=!0,n&&(t[u].defaultSelected=!0);return}e!==null||t[u].disabled||(e=t[u])}e!==null&&(e.selected=!0)}}function Zf(t,e,a){if(e!=null&&(e=""+xe(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+xe(a):""}function Vf(t,e,a,n){if(e==null){if(n!=null){if(a!=null)throw Error(s(92));if(Gt(n)){if(1<n.length)throw Error(s(93));n=n[0]}a=n}a==null&&(a=""),e=a}a=xe(e),t.defaultValue=a,n=t.textContent,n===a&&n!==""&&n!==null&&(t.value=n)}function An(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var Qg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function $f(t,e,a){var n=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,a):typeof a!="number"||a===0||Qg.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function Kf(t,e,a){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,a!=null){for(var n in a)!a.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var u in e)n=e[u],e.hasOwnProperty(u)&&a[u]!==n&&$f(t,u,n)}else for(var c in e)e.hasOwnProperty(c)&&$f(t,c,e[c])}function Rs(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Zg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Vg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Du(t){return Vg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var xs=null;function Ms(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var En=null,wn=null;function Jf(t){var e=_n(t);if(e&&(t=e.stateNode)){var a=t[re]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ts(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Me(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var n=a[e];if(n!==t&&n.form===t.form){var u=n[re]||null;if(!u)throw Error(s(90));Ts(n,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(e=0;e<a.length;e++)n=a[e],n.form===t.form&&Gf(n)}break t;case"textarea":Zf(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&Sn(t,!!a.multiple,e,!1)}}}var Ns=!1;function kf(t,e,a){if(Ns)return t(e,a);Ns=!0;try{var n=t(e);return n}finally{if(Ns=!1,(En!==null||wn!==null)&&(gi(),En&&(e=En,t=wn,wn=En=null,Jf(e),t)))for(e=0;e<t.length;e++)Jf(t[e])}}function ol(t,e){var a=t.stateNode;if(a===null)return null;var n=a[re]||null;if(n===null)return null;a=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(s(231,e,typeof a));return a}var ta=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ds=!1;if(ta)try{var dl={};Object.defineProperty(dl,"passive",{get:function(){Ds=!0}}),window.addEventListener("test",dl,dl),window.removeEventListener("test",dl,dl)}catch{Ds=!1}var ba=null,Cs=null,Cu=null;function Wf(){if(Cu)return Cu;var t,e=Cs,a=e.length,n,u="value"in ba?ba.value:ba.textContent,c=u.length;for(t=0;t<a&&e[t]===u[t];t++);var o=a-t;for(n=1;n<=o&&e[a-n]===u[c-n];n++);return Cu=u.slice(t,1<n?1-n:void 0)}function zu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Uu(){return!0}function Ff(){return!1}function fe(t){function e(a,n,u,c,o){this._reactName=a,this._targetInst=u,this.type=n,this.nativeEvent=c,this.target=o,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(a=t[h],this[h]=a?a(c):c[h]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Uu:Ff,this.isPropagationStopped=Ff,this}return R(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Uu)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Uu)},persist:function(){},isPersistent:Uu}),e}var $a={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bu=fe($a),hl=R({},$a,{view:0,detail:0}),$g=fe(hl),zs,Us,ml,Hu=R({},hl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hs,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ml&&(ml&&t.type==="mousemove"?(zs=t.screenX-ml.screenX,Us=t.screenY-ml.screenY):Us=zs=0,ml=t),zs)},movementY:function(t){return"movementY"in t?t.movementY:Us}}),If=fe(Hu),Kg=R({},Hu,{dataTransfer:0}),Jg=fe(Kg),kg=R({},hl,{relatedTarget:0}),Bs=fe(kg),Wg=R({},$a,{animationName:0,elapsedTime:0,pseudoElement:0}),Fg=fe(Wg),Ig=R({},$a,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Pg=fe(Ig),t0=R({},$a,{data:0}),Pf=fe(t0),e0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},a0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},n0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function l0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=n0[t])?!!e[t]:!1}function Hs(){return l0}var u0=R({},hl,{key:function(t){if(t.key){var e=e0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=zu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?a0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hs,charCode:function(t){return t.type==="keypress"?zu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?zu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),i0=fe(u0),s0=R({},Hu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),to=fe(s0),c0=R({},hl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hs}),r0=fe(c0),f0=R({},$a,{propertyName:0,elapsedTime:0,pseudoElement:0}),o0=fe(f0),d0=R({},Hu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),h0=fe(d0),m0=R({},$a,{newState:0,oldState:0}),y0=fe(m0),g0=[9,13,27,32],qs=ta&&"CompositionEvent"in window,yl=null;ta&&"documentMode"in document&&(yl=document.documentMode);var _0=ta&&"TextEvent"in window&&!yl,eo=ta&&(!qs||yl&&8<yl&&11>=yl),ao=" ",no=!1;function lo(t,e){switch(t){case"keyup":return g0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function uo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Tn=!1;function v0(t,e){switch(t){case"compositionend":return uo(e);case"keypress":return e.which!==32?null:(no=!0,ao);case"textInput":return t=e.data,t===ao&&no?null:t;default:return null}}function p0(t,e){if(Tn)return t==="compositionend"||!qs&&lo(t,e)?(t=Wf(),Cu=Cs=ba=null,Tn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return eo&&e.locale!=="ko"?null:e.data;default:return null}}var b0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function io(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!b0[t.type]:e==="textarea"}function so(t,e,a,n){En?wn?wn.push(n):wn=[n]:En=n,e=Ai(e,"onChange"),0<e.length&&(a=new Bu("onChange","change",null,a,n),t.push({event:a,listeners:e}))}var gl=null,_l=null;function S0(t){Gh(t,0)}function qu(t){var e=fl(t);if(Gf(e))return t}function co(t,e){if(t==="change")return e}var ro=!1;if(ta){var js;if(ta){var Ls="oninput"in document;if(!Ls){var fo=document.createElement("div");fo.setAttribute("oninput","return;"),Ls=typeof fo.oninput=="function"}js=Ls}else js=!1;ro=js&&(!document.documentMode||9<document.documentMode)}function oo(){gl&&(gl.detachEvent("onpropertychange",ho),_l=gl=null)}function ho(t){if(t.propertyName==="value"&&qu(_l)){var e=[];so(e,_l,t,Ms(t)),kf(S0,e)}}function A0(t,e,a){t==="focusin"?(oo(),gl=e,_l=a,gl.attachEvent("onpropertychange",ho)):t==="focusout"&&oo()}function E0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return qu(_l)}function w0(t,e){if(t==="click")return qu(e)}function T0(t,e){if(t==="input"||t==="change")return qu(e)}function O0(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ve=typeof Object.is=="function"?Object.is:O0;function vl(t,e){if(ve(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),n=Object.keys(e);if(a.length!==n.length)return!1;for(n=0;n<a.length;n++){var u=a[n];if(!ms.call(e,u)||!ve(t[u],e[u]))return!1}return!0}function mo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function yo(t,e){var a=mo(t);t=0;for(var n;a;){if(a.nodeType===3){if(n=t+a.textContent.length,t<=e&&n>=e)return{node:a,offset:e-t};t=n}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=mo(a)}}function go(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?go(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function _o(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Nu(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=Nu(t.document)}return e}function Ys(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var R0=ta&&"documentMode"in document&&11>=document.documentMode,On=null,Xs=null,pl=null,Gs=!1;function vo(t,e,a){var n=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Gs||On==null||On!==Nu(n)||(n=On,"selectionStart"in n&&Ys(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),pl&&vl(pl,n)||(pl=n,n=Ai(Xs,"onSelect"),0<n.length&&(e=new Bu("onSelect","select",null,e,a),t.push({event:e,listeners:n}),e.target=On)))}function Ka(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Rn={animationend:Ka("Animation","AnimationEnd"),animationiteration:Ka("Animation","AnimationIteration"),animationstart:Ka("Animation","AnimationStart"),transitionrun:Ka("Transition","TransitionRun"),transitionstart:Ka("Transition","TransitionStart"),transitioncancel:Ka("Transition","TransitionCancel"),transitionend:Ka("Transition","TransitionEnd")},Qs={},po={};ta&&(po=document.createElement("div").style,"AnimationEvent"in window||(delete Rn.animationend.animation,delete Rn.animationiteration.animation,delete Rn.animationstart.animation),"TransitionEvent"in window||delete Rn.transitionend.transition);function Ja(t){if(Qs[t])return Qs[t];if(!Rn[t])return t;var e=Rn[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in po)return Qs[t]=e[a];return t}var bo=Ja("animationend"),So=Ja("animationiteration"),Ao=Ja("animationstart"),x0=Ja("transitionrun"),M0=Ja("transitionstart"),N0=Ja("transitioncancel"),Eo=Ja("transitionend"),wo=new Map,Zs="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Zs.push("scrollEnd");function Le(t,e){wo.set(t,e),Va(e,[t])}var To=new WeakMap;function Ne(t,e){if(typeof t=="object"&&t!==null){var a=To.get(t);return a!==void 0?a:(e={value:t,source:e,stack:Yf(e)},To.set(t,e),e)}return{value:t,source:e,stack:Yf(e)}}var De=[],xn=0,Vs=0;function ju(){for(var t=xn,e=Vs=xn=0;e<t;){var a=De[e];De[e++]=null;var n=De[e];De[e++]=null;var u=De[e];De[e++]=null;var c=De[e];if(De[e++]=null,n!==null&&u!==null){var o=n.pending;o===null?u.next=u:(u.next=o.next,o.next=u),n.pending=u}c!==0&&Oo(a,u,c)}}function Lu(t,e,a,n){De[xn++]=t,De[xn++]=e,De[xn++]=a,De[xn++]=n,Vs|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function $s(t,e,a,n){return Lu(t,e,a,n),Yu(t)}function Mn(t,e){return Lu(t,null,null,e),Yu(t)}function Oo(t,e,a){t.lanes|=a;var n=t.alternate;n!==null&&(n.lanes|=a);for(var u=!1,c=t.return;c!==null;)c.childLanes|=a,n=c.alternate,n!==null&&(n.childLanes|=a),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(u=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,u&&e!==null&&(u=31-_e(a),t=c.hiddenUpdates,n=t[u],n===null?t[u]=[e]:n.push(e),e.lane=a|536870912),c):null}function Yu(t){if(50<Zl)throw Zl=0,Ic=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Nn={};function D0(t,e,a,n){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pe(t,e,a,n){return new D0(t,e,a,n)}function Ks(t){return t=t.prototype,!(!t||!t.isReactComponent)}function ea(t,e){var a=t.alternate;return a===null?(a=pe(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function Ro(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xu(t,e,a,n,u,c){var o=0;if(n=t,typeof t=="function")Ks(t)&&(o=1);else if(typeof t=="string")o=z_(t,a,F.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case mt:return t=pe(31,a,e,u),t.elementType=mt,t.lanes=c,t;case q:return ka(a.children,u,c,e);case Q:o=8,u|=24;break;case Z:return t=pe(12,a,e,u|2),t.elementType=Z,t.lanes=c,t;case k:return t=pe(13,a,e,u),t.elementType=k,t.lanes=c,t;case ct:return t=pe(19,a,e,u),t.elementType=ct,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Et:case J:o=10;break t;case $:o=9;break t;case V:o=11;break t;case tt:o=14;break t;case Rt:o=16,n=null;break t}o=29,a=Error(s(130,t===null?"null":typeof t,"")),n=null}return e=pe(o,a,e,u),e.elementType=t,e.type=n,e.lanes=c,e}function ka(t,e,a,n){return t=pe(7,t,n,e),t.lanes=a,t}function Js(t,e,a){return t=pe(6,t,null,e),t.lanes=a,t}function ks(t,e,a){return e=pe(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Dn=[],Cn=0,Gu=null,Qu=0,Ce=[],ze=0,Wa=null,aa=1,na="";function Fa(t,e){Dn[Cn++]=Qu,Dn[Cn++]=Gu,Gu=t,Qu=e}function xo(t,e,a){Ce[ze++]=aa,Ce[ze++]=na,Ce[ze++]=Wa,Wa=t;var n=aa;t=na;var u=32-_e(n)-1;n&=~(1<<u),a+=1;var c=32-_e(e)+u;if(30<c){var o=u-u%5;c=(n&(1<<o)-1).toString(32),n>>=o,u-=o,aa=1<<32-_e(e)+u|a<<u|n,na=c+t}else aa=1<<c|a<<u|n,na=t}function Ws(t){t.return!==null&&(Fa(t,1),xo(t,1,0))}function Fs(t){for(;t===Gu;)Gu=Dn[--Cn],Dn[Cn]=null,Qu=Dn[--Cn],Dn[Cn]=null;for(;t===Wa;)Wa=Ce[--ze],Ce[ze]=null,na=Ce[--ze],Ce[ze]=null,aa=Ce[--ze],Ce[ze]=null}var se=null,Bt=null,pt=!1,Ia=null,Ze=!1,Is=Error(s(519));function Pa(t){var e=Error(s(418,""));throw Al(Ne(e,t)),Is}function Mo(t){var e=t.stateNode,a=t.type,n=t.memoizedProps;switch(e[ne]=t,e[re]=n,a){case"dialog":ht("cancel",e),ht("close",e);break;case"iframe":case"object":case"embed":ht("load",e);break;case"video":case"audio":for(a=0;a<$l.length;a++)ht($l[a],e);break;case"source":ht("error",e);break;case"img":case"image":case"link":ht("error",e),ht("load",e);break;case"details":ht("toggle",e);break;case"input":ht("invalid",e),Qf(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Mu(e);break;case"select":ht("invalid",e);break;case"textarea":ht("invalid",e),Vf(e,n.value,n.defaultValue,n.children),Mu(e)}a=n.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||n.suppressHydrationWarning===!0||$h(e.textContent,a)?(n.popover!=null&&(ht("beforetoggle",e),ht("toggle",e)),n.onScroll!=null&&ht("scroll",e),n.onScrollEnd!=null&&ht("scrollend",e),n.onClick!=null&&(e.onclick=Ei),e=!0):e=!1,e||Pa(t)}function No(t){for(se=t.return;se;)switch(se.tag){case 5:case 13:Ze=!1;return;case 27:case 3:Ze=!0;return;default:se=se.return}}function bl(t){if(t!==se)return!1;if(!pt)return No(t),pt=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||mr(t.type,t.memoizedProps)),a=!a),a&&Bt&&Pa(t),No(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){Bt=Xe(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}Bt=null}}else e===27?(e=Bt,Ha(t.type)?(t=vr,vr=null,Bt=t):Bt=e):Bt=se?Xe(t.stateNode.nextSibling):null;return!0}function Sl(){Bt=se=null,pt=!1}function Do(){var t=Ia;return t!==null&&(he===null?he=t:he.push.apply(he,t),Ia=null),t}function Al(t){Ia===null?Ia=[t]:Ia.push(t)}var Ps=B(null),tn=null,la=null;function Sa(t,e,a){j(Ps,e._currentValue),e._currentValue=a}function ua(t){t._currentValue=Ps.current,X(Ps)}function tc(t,e,a){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===a)break;t=t.return}}function ec(t,e,a,n){var u=t.child;for(u!==null&&(u.return=t);u!==null;){var c=u.dependencies;if(c!==null){var o=u.child;c=c.firstContext;t:for(;c!==null;){var h=c;c=u;for(var y=0;y<e.length;y++)if(h.context===e[y]){c.lanes|=a,h=c.alternate,h!==null&&(h.lanes|=a),tc(c.return,a,t),n||(o=null);break t}c=h.next}}else if(u.tag===18){if(o=u.return,o===null)throw Error(s(341));o.lanes|=a,c=o.alternate,c!==null&&(c.lanes|=a),tc(o,a,t),o=null}else o=u.child;if(o!==null)o.return=u;else for(o=u;o!==null;){if(o===t){o=null;break}if(u=o.sibling,u!==null){u.return=o.return,o=u;break}o=o.return}u=o}}function El(t,e,a,n){t=null;for(var u=e,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var o=u.alternate;if(o===null)throw Error(s(387));if(o=o.memoizedProps,o!==null){var h=u.type;ve(u.pendingProps.value,o.value)||(t!==null?t.push(h):t=[h])}}else if(u===ye.current){if(o=u.alternate,o===null)throw Error(s(387));o.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(t!==null?t.push(Il):t=[Il])}u=u.return}t!==null&&ec(e,t,a,n),e.flags|=262144}function Zu(t){for(t=t.firstContext;t!==null;){if(!ve(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function en(t){tn=t,la=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function le(t){return Co(tn,t)}function Vu(t,e){return tn===null&&en(t),Co(t,e)}function Co(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},la===null){if(t===null)throw Error(s(308));la=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else la=la.next=e;return a}var C0=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},z0=r.unstable_scheduleCallback,U0=r.unstable_NormalPriority,Qt={$$typeof:J,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ac(){return{controller:new C0,data:new Map,refCount:0}}function wl(t){t.refCount--,t.refCount===0&&z0(U0,function(){t.controller.abort()})}var Tl=null,nc=0,zn=0,Un=null;function B0(t,e){if(Tl===null){var a=Tl=[];nc=0,zn=ur(),Un={status:"pending",value:void 0,then:function(n){a.push(n)}}}return nc++,e.then(zo,zo),e}function zo(){if(--nc===0&&Tl!==null){Un!==null&&(Un.status="fulfilled");var t=Tl;Tl=null,zn=0,Un=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function H0(t,e){var a=[],n={status:"pending",value:null,reason:null,then:function(u){a.push(u)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var u=0;u<a.length;u++)(0,a[u])(e)},function(u){for(n.status="rejected",n.reason=u,u=0;u<a.length;u++)(0,a[u])(void 0)}),n}var Uo=N.S;N.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&B0(t,e),Uo!==null&&Uo(t,e)};var an=B(null);function lc(){var t=an.current;return t!==null?t:Nt.pooledCache}function $u(t,e){e===null?j(an,an.current):j(an,e.pool)}function Bo(){var t=lc();return t===null?null:{parent:Qt._currentValue,pool:t}}var Ol=Error(s(460)),Ho=Error(s(474)),Ku=Error(s(542)),uc={then:function(){}};function qo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ju(){}function jo(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(Ju,Ju),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Yo(t),t;default:if(typeof e.status=="string")e.then(Ju,Ju);else{if(t=Nt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var u=e;u.status="fulfilled",u.value=n}},function(n){if(e.status==="pending"){var u=e;u.status="rejected",u.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Yo(t),t}throw Rl=e,Ol}}var Rl=null;function Lo(){if(Rl===null)throw Error(s(459));var t=Rl;return Rl=null,t}function Yo(t){if(t===Ol||t===Ku)throw Error(s(483))}var Aa=!1;function ic(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function sc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ea(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function wa(t,e,a){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,(St&2)!==0){var u=n.pending;return u===null?e.next=e:(e.next=u.next,u.next=e),n.pending=e,e=Yu(t),Oo(t,null,a),e}return Lu(t,n,e,a),Yu(t)}function xl(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,a|=n,e.lanes=a,Cf(t,a)}}function cc(t,e){var a=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,a===n)){var u=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var o={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?u=c=o:c=c.next=o,a=a.next}while(a!==null);c===null?u=c=e:c=c.next=e}else u=c=e;a={baseState:n.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:n.shared,callbacks:n.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var rc=!1;function Ml(){if(rc){var t=Un;if(t!==null)throw t}}function Nl(t,e,a,n){rc=!1;var u=t.updateQueue;Aa=!1;var c=u.firstBaseUpdate,o=u.lastBaseUpdate,h=u.shared.pending;if(h!==null){u.shared.pending=null;var y=h,E=y.next;y.next=null,o===null?c=E:o.next=E,o=y;var x=t.alternate;x!==null&&(x=x.updateQueue,h=x.lastBaseUpdate,h!==o&&(h===null?x.firstBaseUpdate=E:h.next=E,x.lastBaseUpdate=y))}if(c!==null){var z=u.baseState;o=0,x=E=y=null,h=c;do{var w=h.lane&-536870913,T=w!==h.lane;if(T?(yt&w)===w:(n&w)===w){w!==0&&w===zn&&(rc=!0),x!==null&&(x=x.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var at=t,I=h;w=e;var Ot=a;switch(I.tag){case 1:if(at=I.payload,typeof at=="function"){z=at.call(Ot,z,w);break t}z=at;break t;case 3:at.flags=at.flags&-65537|128;case 0:if(at=I.payload,w=typeof at=="function"?at.call(Ot,z,w):at,w==null)break t;z=R({},z,w);break t;case 2:Aa=!0}}w=h.callback,w!==null&&(t.flags|=64,T&&(t.flags|=8192),T=u.callbacks,T===null?u.callbacks=[w]:T.push(w))}else T={lane:w,tag:h.tag,payload:h.payload,callback:h.callback,next:null},x===null?(E=x=T,y=z):x=x.next=T,o|=w;if(h=h.next,h===null){if(h=u.shared.pending,h===null)break;T=h,h=T.next,T.next=null,u.lastBaseUpdate=T,u.shared.pending=null}}while(!0);x===null&&(y=z),u.baseState=y,u.firstBaseUpdate=E,u.lastBaseUpdate=x,c===null&&(u.shared.lanes=0),Ca|=o,t.lanes=o,t.memoizedState=z}}function Xo(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function Go(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)Xo(a[t],e)}var Bn=B(null),ku=B(0);function Qo(t,e){t=da,j(ku,t),j(Bn,e),da=t|e.baseLanes}function fc(){j(ku,da),j(Bn,Bn.current)}function oc(){da=ku.current,X(Bn),X(ku)}var Ta=0,rt=null,wt=null,Yt=null,Wu=!1,Hn=!1,nn=!1,Fu=0,Dl=0,qn=null,q0=0;function qt(){throw Error(s(321))}function dc(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!ve(t[a],e[a]))return!1;return!0}function hc(t,e,a,n,u,c){return Ta=c,rt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,N.H=t===null||t.memoizedState===null?Od:Rd,nn=!1,c=a(n,u),nn=!1,Hn&&(c=Vo(e,a,n,u)),Zo(t),c}function Zo(t){N.H=ni;var e=wt!==null&&wt.next!==null;if(Ta=0,Yt=wt=rt=null,Wu=!1,Dl=0,qn=null,e)throw Error(s(300));t===null||Jt||(t=t.dependencies,t!==null&&Zu(t)&&(Jt=!0))}function Vo(t,e,a,n){rt=t;var u=0;do{if(Hn&&(qn=null),Dl=0,Hn=!1,25<=u)throw Error(s(301));if(u+=1,Yt=wt=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}N.H=Z0,c=e(a,n)}while(Hn);return c}function j0(){var t=N.H,e=t.useState()[0];return e=typeof e.then=="function"?Cl(e):e,t=t.useState()[0],(wt!==null?wt.memoizedState:null)!==t&&(rt.flags|=1024),e}function mc(){var t=Fu!==0;return Fu=0,t}function yc(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function gc(t){if(Wu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Wu=!1}Ta=0,Yt=wt=rt=null,Hn=!1,Dl=Fu=0,qn=null}function oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Yt===null?rt.memoizedState=Yt=t:Yt=Yt.next=t,Yt}function Xt(){if(wt===null){var t=rt.alternate;t=t!==null?t.memoizedState:null}else t=wt.next;var e=Yt===null?rt.memoizedState:Yt.next;if(e!==null)Yt=e,wt=t;else{if(t===null)throw rt.alternate===null?Error(s(467)):Error(s(310));wt=t,t={memoizedState:wt.memoizedState,baseState:wt.baseState,baseQueue:wt.baseQueue,queue:wt.queue,next:null},Yt===null?rt.memoizedState=Yt=t:Yt=Yt.next=t}return Yt}function _c(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Cl(t){var e=Dl;return Dl+=1,qn===null&&(qn=[]),t=jo(qn,t,e),e=rt,(Yt===null?e.memoizedState:Yt.next)===null&&(e=e.alternate,N.H=e===null||e.memoizedState===null?Od:Rd),t}function Iu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Cl(t);if(t.$$typeof===J)return le(t)}throw Error(s(438,String(t)))}function vc(t){var e=null,a=rt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var n=rt.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(u){return u.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=_c(),rt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),n=0;n<t;n++)a[n]=bt;return e.index++,a}function ia(t,e){return typeof e=="function"?e(t):e}function Pu(t){var e=Xt();return pc(e,wt,t)}function pc(t,e,a){var n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=a;var u=t.baseQueue,c=n.pending;if(c!==null){if(u!==null){var o=u.next;u.next=c.next,c.next=o}e.baseQueue=u=c,n.pending=null}if(c=t.baseState,u===null)t.memoizedState=c;else{e=u.next;var h=o=null,y=null,E=e,x=!1;do{var z=E.lane&-536870913;if(z!==E.lane?(yt&z)===z:(Ta&z)===z){var w=E.revertLane;if(w===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),z===zn&&(x=!0);else if((Ta&w)===w){E=E.next,w===zn&&(x=!0);continue}else z={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},y===null?(h=y=z,o=c):y=y.next=z,rt.lanes|=w,Ca|=w;z=E.action,nn&&a(c,z),c=E.hasEagerState?E.eagerState:a(c,z)}else w={lane:z,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},y===null?(h=y=w,o=c):y=y.next=w,rt.lanes|=z,Ca|=z;E=E.next}while(E!==null&&E!==e);if(y===null?o=c:y.next=h,!ve(c,t.memoizedState)&&(Jt=!0,x&&(a=Un,a!==null)))throw a;t.memoizedState=c,t.baseState=o,t.baseQueue=y,n.lastRenderedState=c}return u===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function bc(t){var e=Xt(),a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=t;var n=a.dispatch,u=a.pending,c=e.memoizedState;if(u!==null){a.pending=null;var o=u=u.next;do c=t(c,o.action),o=o.next;while(o!==u);ve(c,e.memoizedState)||(Jt=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),a.lastRenderedState=c}return[c,n]}function $o(t,e,a){var n=rt,u=Xt(),c=pt;if(c){if(a===void 0)throw Error(s(407));a=a()}else a=e();var o=!ve((wt||u).memoizedState,a);o&&(u.memoizedState=a,Jt=!0),u=u.queue;var h=ko.bind(null,n,u,t);if(zl(2048,8,h,[t]),u.getSnapshot!==e||o||Yt!==null&&Yt.memoizedState.tag&1){if(n.flags|=2048,jn(9,ti(),Jo.bind(null,n,u,a,e),null),Nt===null)throw Error(s(349));c||(Ta&124)!==0||Ko(n,e,a)}return a}function Ko(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=rt.updateQueue,e===null?(e=_c(),rt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function Jo(t,e,a,n){e.value=a,e.getSnapshot=n,Wo(e)&&Fo(t)}function ko(t,e,a){return a(function(){Wo(e)&&Fo(t)})}function Wo(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!ve(t,a)}catch{return!0}}function Fo(t){var e=Mn(t,2);e!==null&&we(e,t,2)}function Sc(t){var e=oe();if(typeof t=="function"){var a=t;if(t=a(),nn){va(!0);try{a()}finally{va(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ia,lastRenderedState:t},e}function Io(t,e,a,n){return t.baseState=a,pc(t,wt,typeof n=="function"?n:ia)}function L0(t,e,a,n,u){if(ai(t))throw Error(s(485));if(t=e.action,t!==null){var c={payload:u,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){c.listeners.push(o)}};N.T!==null?a(!0):c.isTransition=!1,n(c),a=e.pending,a===null?(c.next=e.pending=c,Po(e,c)):(c.next=a.next,e.pending=a.next=c)}}function Po(t,e){var a=e.action,n=e.payload,u=t.state;if(e.isTransition){var c=N.T,o={};N.T=o;try{var h=a(u,n),y=N.S;y!==null&&y(o,h),td(t,e,h)}catch(E){Ac(t,e,E)}finally{N.T=c}}else try{c=a(u,n),td(t,e,c)}catch(E){Ac(t,e,E)}}function td(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(n){ed(t,e,n)},function(n){return Ac(t,e,n)}):ed(t,e,a)}function ed(t,e,a){e.status="fulfilled",e.value=a,ad(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,Po(t,a)))}function Ac(t,e,a){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=a,ad(e),e=e.next;while(e!==n)}t.action=null}function ad(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function nd(t,e){return e}function ld(t,e){if(pt){var a=Nt.formState;if(a!==null){t:{var n=rt;if(pt){if(Bt){e:{for(var u=Bt,c=Ze;u.nodeType!==8;){if(!c){u=null;break e}if(u=Xe(u.nextSibling),u===null){u=null;break e}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){Bt=Xe(u.nextSibling),n=u.data==="F!";break t}}Pa(n)}n=!1}n&&(e=a[0])}}return a=oe(),a.memoizedState=a.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:nd,lastRenderedState:e},a.queue=n,a=Ed.bind(null,rt,n),n.dispatch=a,n=Sc(!1),c=Rc.bind(null,rt,!1,n.queue),n=oe(),u={state:e,dispatch:null,action:t,pending:null},n.queue=u,a=L0.bind(null,rt,u,c,a),u.dispatch=a,n.memoizedState=t,[e,a,!1]}function ud(t){var e=Xt();return id(e,wt,t)}function id(t,e,a){if(e=pc(t,e,nd)[0],t=Pu(ia)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=Cl(e)}catch(o){throw o===Ol?Ku:o}else n=e;e=Xt();var u=e.queue,c=u.dispatch;return a!==e.memoizedState&&(rt.flags|=2048,jn(9,ti(),Y0.bind(null,u,a),null)),[n,c,t]}function Y0(t,e){t.action=e}function sd(t){var e=Xt(),a=wt;if(a!==null)return id(e,a,t);Xt(),e=e.memoizedState,a=Xt();var n=a.queue.dispatch;return a.memoizedState=t,[e,n,!1]}function jn(t,e,a,n){return t={tag:t,create:a,deps:n,inst:e,next:null},e=rt.updateQueue,e===null&&(e=_c(),rt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(n=a.next,a.next=t,t.next=n,e.lastEffect=t),t}function ti(){return{destroy:void 0,resource:void 0}}function cd(){return Xt().memoizedState}function ei(t,e,a,n){var u=oe();n=n===void 0?null:n,rt.flags|=t,u.memoizedState=jn(1|e,ti(),a,n)}function zl(t,e,a,n){var u=Xt();n=n===void 0?null:n;var c=u.memoizedState.inst;wt!==null&&n!==null&&dc(n,wt.memoizedState.deps)?u.memoizedState=jn(e,c,a,n):(rt.flags|=t,u.memoizedState=jn(1|e,c,a,n))}function rd(t,e){ei(8390656,8,t,e)}function fd(t,e){zl(2048,8,t,e)}function od(t,e){return zl(4,2,t,e)}function dd(t,e){return zl(4,4,t,e)}function hd(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function md(t,e,a){a=a!=null?a.concat([t]):null,zl(4,4,hd.bind(null,e,t),a)}function Ec(){}function yd(t,e){var a=Xt();e=e===void 0?null:e;var n=a.memoizedState;return e!==null&&dc(e,n[1])?n[0]:(a.memoizedState=[t,e],t)}function gd(t,e){var a=Xt();e=e===void 0?null:e;var n=a.memoizedState;if(e!==null&&dc(e,n[1]))return n[0];if(n=t(),nn){va(!0);try{t()}finally{va(!1)}}return a.memoizedState=[n,e],n}function wc(t,e,a){return a===void 0||(Ta&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=ph(),rt.lanes|=t,Ca|=t,a)}function _d(t,e,a,n){return ve(a,e)?a:Bn.current!==null?(t=wc(t,a,n),ve(t,e)||(Jt=!0),t):(Ta&42)===0?(Jt=!0,t.memoizedState=a):(t=ph(),rt.lanes|=t,Ca|=t,e)}function vd(t,e,a,n,u){var c=Y.p;Y.p=c!==0&&8>c?c:8;var o=N.T,h={};N.T=h,Rc(t,!1,e,a);try{var y=u(),E=N.S;if(E!==null&&E(h,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var x=H0(y,n);Ul(t,e,x,Ee(t))}else Ul(t,e,n,Ee(t))}catch(z){Ul(t,e,{then:function(){},status:"rejected",reason:z},Ee())}finally{Y.p=c,N.T=o}}function X0(){}function Tc(t,e,a,n){if(t.tag!==5)throw Error(s(476));var u=pd(t).queue;vd(t,u,e,et,a===null?X0:function(){return bd(t),a(n)})}function pd(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:et,baseState:et,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ia,lastRenderedState:et},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ia,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function bd(t){var e=pd(t).next.queue;Ul(t,e,{},Ee())}function Oc(){return le(Il)}function Sd(){return Xt().memoizedState}function Ad(){return Xt().memoizedState}function G0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=Ee();t=Ea(a);var n=wa(e,t,a);n!==null&&(we(n,e,a),xl(n,e,a)),e={cache:ac()},t.payload=e;return}e=e.return}}function Q0(t,e,a){var n=Ee();a={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ai(t)?wd(e,a):(a=$s(t,e,a,n),a!==null&&(we(a,t,n),Td(a,e,n)))}function Ed(t,e,a){var n=Ee();Ul(t,e,a,n)}function Ul(t,e,a,n){var u={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ai(t))wd(e,u);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var o=e.lastRenderedState,h=c(o,a);if(u.hasEagerState=!0,u.eagerState=h,ve(h,o))return Lu(t,e,u,0),Nt===null&&ju(),!1}catch{}finally{}if(a=$s(t,e,u,n),a!==null)return we(a,t,n),Td(a,e,n),!0}return!1}function Rc(t,e,a,n){if(n={lane:2,revertLane:ur(),action:n,hasEagerState:!1,eagerState:null,next:null},ai(t)){if(e)throw Error(s(479))}else e=$s(t,a,n,2),e!==null&&we(e,t,2)}function ai(t){var e=t.alternate;return t===rt||e!==null&&e===rt}function wd(t,e){Hn=Wu=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function Td(t,e,a){if((a&4194048)!==0){var n=e.lanes;n&=t.pendingLanes,a|=n,e.lanes=a,Cf(t,a)}}var ni={readContext:le,use:Iu,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},Od={readContext:le,use:Iu,useCallback:function(t,e){return oe().memoizedState=[t,e===void 0?null:e],t},useContext:le,useEffect:rd,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,ei(4194308,4,hd.bind(null,e,t),a)},useLayoutEffect:function(t,e){return ei(4194308,4,t,e)},useInsertionEffect:function(t,e){ei(4,2,t,e)},useMemo:function(t,e){var a=oe();e=e===void 0?null:e;var n=t();if(nn){va(!0);try{t()}finally{va(!1)}}return a.memoizedState=[n,e],n},useReducer:function(t,e,a){var n=oe();if(a!==void 0){var u=a(e);if(nn){va(!0);try{a(e)}finally{va(!1)}}}else u=e;return n.memoizedState=n.baseState=u,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:u},n.queue=t,t=t.dispatch=Q0.bind(null,rt,t),[n.memoizedState,t]},useRef:function(t){var e=oe();return t={current:t},e.memoizedState=t},useState:function(t){t=Sc(t);var e=t.queue,a=Ed.bind(null,rt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:Ec,useDeferredValue:function(t,e){var a=oe();return wc(a,t,e)},useTransition:function(){var t=Sc(!1);return t=vd.bind(null,rt,t.queue,!0,!1),oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var n=rt,u=oe();if(pt){if(a===void 0)throw Error(s(407));a=a()}else{if(a=e(),Nt===null)throw Error(s(349));(yt&124)!==0||Ko(n,e,a)}u.memoizedState=a;var c={value:a,getSnapshot:e};return u.queue=c,rd(ko.bind(null,n,c,t),[t]),n.flags|=2048,jn(9,ti(),Jo.bind(null,n,c,a,e),null),a},useId:function(){var t=oe(),e=Nt.identifierPrefix;if(pt){var a=na,n=aa;a=(n&~(1<<32-_e(n)-1)).toString(32)+a,e="«"+e+"R"+a,a=Fu++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=q0++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Oc,useFormState:ld,useActionState:ld,useOptimistic:function(t){var e=oe();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=Rc.bind(null,rt,!0,a),a.dispatch=e,[t,e]},useMemoCache:vc,useCacheRefresh:function(){return oe().memoizedState=G0.bind(null,rt)}},Rd={readContext:le,use:Iu,useCallback:yd,useContext:le,useEffect:fd,useImperativeHandle:md,useInsertionEffect:od,useLayoutEffect:dd,useMemo:gd,useReducer:Pu,useRef:cd,useState:function(){return Pu(ia)},useDebugValue:Ec,useDeferredValue:function(t,e){var a=Xt();return _d(a,wt.memoizedState,t,e)},useTransition:function(){var t=Pu(ia)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:Cl(t),e]},useSyncExternalStore:$o,useId:Sd,useHostTransitionStatus:Oc,useFormState:ud,useActionState:ud,useOptimistic:function(t,e){var a=Xt();return Io(a,wt,t,e)},useMemoCache:vc,useCacheRefresh:Ad},Z0={readContext:le,use:Iu,useCallback:yd,useContext:le,useEffect:fd,useImperativeHandle:md,useInsertionEffect:od,useLayoutEffect:dd,useMemo:gd,useReducer:bc,useRef:cd,useState:function(){return bc(ia)},useDebugValue:Ec,useDeferredValue:function(t,e){var a=Xt();return wt===null?wc(a,t,e):_d(a,wt.memoizedState,t,e)},useTransition:function(){var t=bc(ia)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:Cl(t),e]},useSyncExternalStore:$o,useId:Sd,useHostTransitionStatus:Oc,useFormState:sd,useActionState:sd,useOptimistic:function(t,e){var a=Xt();return wt!==null?Io(a,wt,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:vc,useCacheRefresh:Ad},Ln=null,Bl=0;function li(t){var e=Bl;return Bl+=1,Ln===null&&(Ln=[]),jo(Ln,t,e)}function Hl(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ui(t,e){throw e.$$typeof===M?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function xd(t){var e=t._init;return e(t._payload)}function Md(t){function e(p,_){if(t){var S=p.deletions;S===null?(p.deletions=[_],p.flags|=16):S.push(_)}}function a(p,_){if(!t)return null;for(;_!==null;)e(p,_),_=_.sibling;return null}function n(p){for(var _=new Map;p!==null;)p.key!==null?_.set(p.key,p):_.set(p.index,p),p=p.sibling;return _}function u(p,_){return p=ea(p,_),p.index=0,p.sibling=null,p}function c(p,_,S){return p.index=S,t?(S=p.alternate,S!==null?(S=S.index,S<_?(p.flags|=67108866,_):S):(p.flags|=67108866,_)):(p.flags|=1048576,_)}function o(p){return t&&p.alternate===null&&(p.flags|=67108866),p}function h(p,_,S,D){return _===null||_.tag!==6?(_=Js(S,p.mode,D),_.return=p,_):(_=u(_,S),_.return=p,_)}function y(p,_,S,D){var G=S.type;return G===q?x(p,_,S.props.children,D,S.key):_!==null&&(_.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===Rt&&xd(G)===_.type)?(_=u(_,S.props),Hl(_,S),_.return=p,_):(_=Xu(S.type,S.key,S.props,null,p.mode,D),Hl(_,S),_.return=p,_)}function E(p,_,S,D){return _===null||_.tag!==4||_.stateNode.containerInfo!==S.containerInfo||_.stateNode.implementation!==S.implementation?(_=ks(S,p.mode,D),_.return=p,_):(_=u(_,S.children||[]),_.return=p,_)}function x(p,_,S,D,G){return _===null||_.tag!==7?(_=ka(S,p.mode,D,G),_.return=p,_):(_=u(_,S),_.return=p,_)}function z(p,_,S){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=Js(""+_,p.mode,S),_.return=p,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case U:return S=Xu(_.type,_.key,_.props,null,p.mode,S),Hl(S,_),S.return=p,S;case C:return _=ks(_,p.mode,S),_.return=p,_;case Rt:var D=_._init;return _=D(_._payload),z(p,_,S)}if(Gt(_)||zt(_))return _=ka(_,p.mode,S,null),_.return=p,_;if(typeof _.then=="function")return z(p,li(_),S);if(_.$$typeof===J)return z(p,Vu(p,_),S);ui(p,_)}return null}function w(p,_,S,D){var G=_!==null?_.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return G!==null?null:h(p,_,""+S,D);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case U:return S.key===G?y(p,_,S,D):null;case C:return S.key===G?E(p,_,S,D):null;case Rt:return G=S._init,S=G(S._payload),w(p,_,S,D)}if(Gt(S)||zt(S))return G!==null?null:x(p,_,S,D,null);if(typeof S.then=="function")return w(p,_,li(S),D);if(S.$$typeof===J)return w(p,_,Vu(p,S),D);ui(p,S)}return null}function T(p,_,S,D,G){if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return p=p.get(S)||null,h(_,p,""+D,G);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case U:return p=p.get(D.key===null?S:D.key)||null,y(_,p,D,G);case C:return p=p.get(D.key===null?S:D.key)||null,E(_,p,D,G);case Rt:var ft=D._init;return D=ft(D._payload),T(p,_,S,D,G)}if(Gt(D)||zt(D))return p=p.get(S)||null,x(_,p,D,G,null);if(typeof D.then=="function")return T(p,_,S,li(D),G);if(D.$$typeof===J)return T(p,_,S,Vu(_,D),G);ui(_,D)}return null}function at(p,_,S,D){for(var G=null,ft=null,K=_,P=_=0,Wt=null;K!==null&&P<S.length;P++){K.index>P?(Wt=K,K=null):Wt=K.sibling;var vt=w(p,K,S[P],D);if(vt===null){K===null&&(K=Wt);break}t&&K&&vt.alternate===null&&e(p,K),_=c(vt,_,P),ft===null?G=vt:ft.sibling=vt,ft=vt,K=Wt}if(P===S.length)return a(p,K),pt&&Fa(p,P),G;if(K===null){for(;P<S.length;P++)K=z(p,S[P],D),K!==null&&(_=c(K,_,P),ft===null?G=K:ft.sibling=K,ft=K);return pt&&Fa(p,P),G}for(K=n(K);P<S.length;P++)Wt=T(K,p,P,S[P],D),Wt!==null&&(t&&Wt.alternate!==null&&K.delete(Wt.key===null?P:Wt.key),_=c(Wt,_,P),ft===null?G=Wt:ft.sibling=Wt,ft=Wt);return t&&K.forEach(function(Xa){return e(p,Xa)}),pt&&Fa(p,P),G}function I(p,_,S,D){if(S==null)throw Error(s(151));for(var G=null,ft=null,K=_,P=_=0,Wt=null,vt=S.next();K!==null&&!vt.done;P++,vt=S.next()){K.index>P?(Wt=K,K=null):Wt=K.sibling;var Xa=w(p,K,vt.value,D);if(Xa===null){K===null&&(K=Wt);break}t&&K&&Xa.alternate===null&&e(p,K),_=c(Xa,_,P),ft===null?G=Xa:ft.sibling=Xa,ft=Xa,K=Wt}if(vt.done)return a(p,K),pt&&Fa(p,P),G;if(K===null){for(;!vt.done;P++,vt=S.next())vt=z(p,vt.value,D),vt!==null&&(_=c(vt,_,P),ft===null?G=vt:ft.sibling=vt,ft=vt);return pt&&Fa(p,P),G}for(K=n(K);!vt.done;P++,vt=S.next())vt=T(K,p,P,vt.value,D),vt!==null&&(t&&vt.alternate!==null&&K.delete(vt.key===null?P:vt.key),_=c(vt,_,P),ft===null?G=vt:ft.sibling=vt,ft=vt);return t&&K.forEach(function(V_){return e(p,V_)}),pt&&Fa(p,P),G}function Ot(p,_,S,D){if(typeof S=="object"&&S!==null&&S.type===q&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case U:t:{for(var G=S.key;_!==null;){if(_.key===G){if(G=S.type,G===q){if(_.tag===7){a(p,_.sibling),D=u(_,S.props.children),D.return=p,p=D;break t}}else if(_.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===Rt&&xd(G)===_.type){a(p,_.sibling),D=u(_,S.props),Hl(D,S),D.return=p,p=D;break t}a(p,_);break}else e(p,_);_=_.sibling}S.type===q?(D=ka(S.props.children,p.mode,D,S.key),D.return=p,p=D):(D=Xu(S.type,S.key,S.props,null,p.mode,D),Hl(D,S),D.return=p,p=D)}return o(p);case C:t:{for(G=S.key;_!==null;){if(_.key===G)if(_.tag===4&&_.stateNode.containerInfo===S.containerInfo&&_.stateNode.implementation===S.implementation){a(p,_.sibling),D=u(_,S.children||[]),D.return=p,p=D;break t}else{a(p,_);break}else e(p,_);_=_.sibling}D=ks(S,p.mode,D),D.return=p,p=D}return o(p);case Rt:return G=S._init,S=G(S._payload),Ot(p,_,S,D)}if(Gt(S))return at(p,_,S,D);if(zt(S)){if(G=zt(S),typeof G!="function")throw Error(s(150));return S=G.call(S),I(p,_,S,D)}if(typeof S.then=="function")return Ot(p,_,li(S),D);if(S.$$typeof===J)return Ot(p,_,Vu(p,S),D);ui(p,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,_!==null&&_.tag===6?(a(p,_.sibling),D=u(_,S),D.return=p,p=D):(a(p,_),D=Js(S,p.mode,D),D.return=p,p=D),o(p)):a(p,_)}return function(p,_,S,D){try{Bl=0;var G=Ot(p,_,S,D);return Ln=null,G}catch(K){if(K===Ol||K===Ku)throw K;var ft=pe(29,K,null,p.mode);return ft.lanes=D,ft.return=p,ft}finally{}}}var Yn=Md(!0),Nd=Md(!1),Ue=B(null),Ve=null;function Oa(t){var e=t.alternate;j(Zt,Zt.current&1),j(Ue,t),Ve===null&&(e===null||Bn.current!==null||e.memoizedState!==null)&&(Ve=t)}function Dd(t){if(t.tag===22){if(j(Zt,Zt.current),j(Ue,t),Ve===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ve=t)}}else Ra()}function Ra(){j(Zt,Zt.current),j(Ue,Ue.current)}function sa(t){X(Ue),Ve===t&&(Ve=null),X(Zt)}var Zt=B(0);function ii(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||_r(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function xc(t,e,a,n){e=t.memoizedState,a=a(n,e),a=a==null?e:R({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var Mc={enqueueSetState:function(t,e,a){t=t._reactInternals;var n=Ee(),u=Ea(n);u.payload=e,a!=null&&(u.callback=a),e=wa(t,u,n),e!==null&&(we(e,t,n),xl(e,t,n))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var n=Ee(),u=Ea(n);u.tag=1,u.payload=e,a!=null&&(u.callback=a),e=wa(t,u,n),e!==null&&(we(e,t,n),xl(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=Ee(),n=Ea(a);n.tag=2,e!=null&&(n.callback=e),e=wa(t,n,a),e!==null&&(we(e,t,a),xl(e,t,a))}};function Cd(t,e,a,n,u,c,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,c,o):e.prototype&&e.prototype.isPureReactComponent?!vl(a,n)||!vl(u,c):!0}function zd(t,e,a,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,n),e.state!==t&&Mc.enqueueReplaceState(e,e.state,null)}function ln(t,e){var a=e;if("ref"in e){a={};for(var n in e)n!=="ref"&&(a[n]=e[n])}if(t=t.defaultProps){a===e&&(a=R({},a));for(var u in t)a[u]===void 0&&(a[u]=t[u])}return a}var si=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ud(t){si(t)}function Bd(t){console.error(t)}function Hd(t){si(t)}function ci(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function qd(t,e,a){try{var n=t.onCaughtError;n(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function Nc(t,e,a){return a=Ea(a),a.tag=3,a.payload={element:null},a.callback=function(){ci(t,e)},a}function jd(t){return t=Ea(t),t.tag=3,t}function Ld(t,e,a,n){var u=a.type.getDerivedStateFromError;if(typeof u=="function"){var c=n.value;t.payload=function(){return u(c)},t.callback=function(){qd(e,a,n)}}var o=a.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){qd(e,a,n),typeof u!="function"&&(za===null?za=new Set([this]):za.add(this));var h=n.stack;this.componentDidCatch(n.value,{componentStack:h!==null?h:""})})}function V0(t,e,a,n,u){if(a.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=a.alternate,e!==null&&El(e,a,u,!0),a=Ue.current,a!==null){switch(a.tag){case 13:return Ve===null?tr():a.alternate===null&&Ht===0&&(Ht=3),a.flags&=-257,a.flags|=65536,a.lanes=u,n===uc?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([n]):e.add(n),ar(t,n,u)),!1;case 22:return a.flags|=65536,n===uc?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([n]):a.add(n)),ar(t,n,u)),!1}throw Error(s(435,a.tag))}return ar(t,n,u),tr(),!1}if(pt)return e=Ue.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=u,n!==Is&&(t=Error(s(422),{cause:n}),Al(Ne(t,a)))):(n!==Is&&(e=Error(s(423),{cause:n}),Al(Ne(e,a))),t=t.current.alternate,t.flags|=65536,u&=-u,t.lanes|=u,n=Ne(n,a),u=Nc(t.stateNode,n,u),cc(t,u),Ht!==4&&(Ht=2)),!1;var c=Error(s(520),{cause:n});if(c=Ne(c,a),Ql===null?Ql=[c]:Ql.push(c),Ht!==4&&(Ht=2),e===null)return!0;n=Ne(n,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=u&-u,a.lanes|=t,t=Nc(a.stateNode,n,t),cc(a,t),!1;case 1:if(e=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(za===null||!za.has(c))))return a.flags|=65536,u&=-u,a.lanes|=u,u=jd(u),Ld(u,t,a,n),cc(a,u),!1}a=a.return}while(a!==null);return!1}var Yd=Error(s(461)),Jt=!1;function It(t,e,a,n){e.child=t===null?Nd(e,null,a,n):Yn(e,t.child,a,n)}function Xd(t,e,a,n,u){a=a.render;var c=e.ref;if("ref"in n){var o={};for(var h in n)h!=="ref"&&(o[h]=n[h])}else o=n;return en(e),n=hc(t,e,a,o,c,u),h=mc(),t!==null&&!Jt?(yc(t,e,u),ca(t,e,u)):(pt&&h&&Ws(e),e.flags|=1,It(t,e,n,u),e.child)}function Gd(t,e,a,n,u){if(t===null){var c=a.type;return typeof c=="function"&&!Ks(c)&&c.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=c,Qd(t,e,c,n,u)):(t=Xu(a.type,null,n,e,e.mode,u),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!jc(t,u)){var o=c.memoizedProps;if(a=a.compare,a=a!==null?a:vl,a(o,n)&&t.ref===e.ref)return ca(t,e,u)}return e.flags|=1,t=ea(c,n),t.ref=e.ref,t.return=e,e.child=t}function Qd(t,e,a,n,u){if(t!==null){var c=t.memoizedProps;if(vl(c,n)&&t.ref===e.ref)if(Jt=!1,e.pendingProps=n=c,jc(t,u))(t.flags&131072)!==0&&(Jt=!0);else return e.lanes=t.lanes,ca(t,e,u)}return Dc(t,e,a,n,u)}function Zd(t,e,a){var n=e.pendingProps,u=n.children,c=t!==null?t.memoizedState:null;if(n.mode==="hidden"){if((e.flags&128)!==0){if(n=c!==null?c.baseLanes|a:a,t!==null){for(u=e.child=t.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;e.childLanes=c&~n}else e.childLanes=0,e.child=null;return Vd(t,e,n,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&$u(e,c!==null?c.cachePool:null),c!==null?Qo(e,c):fc(),Dd(e);else return e.lanes=e.childLanes=536870912,Vd(t,e,c!==null?c.baseLanes|a:a,a)}else c!==null?($u(e,c.cachePool),Qo(e,c),Ra(),e.memoizedState=null):(t!==null&&$u(e,null),fc(),Ra());return It(t,e,u,a),e.child}function Vd(t,e,a,n){var u=lc();return u=u===null?null:{parent:Qt._currentValue,pool:u},e.memoizedState={baseLanes:a,cachePool:u},t!==null&&$u(e,null),fc(),Dd(e),t!==null&&El(t,e,n,!0),null}function ri(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(s(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function Dc(t,e,a,n,u){return en(e),a=hc(t,e,a,n,void 0,u),n=mc(),t!==null&&!Jt?(yc(t,e,u),ca(t,e,u)):(pt&&n&&Ws(e),e.flags|=1,It(t,e,a,u),e.child)}function $d(t,e,a,n,u,c){return en(e),e.updateQueue=null,a=Vo(e,n,a,u),Zo(t),n=mc(),t!==null&&!Jt?(yc(t,e,c),ca(t,e,c)):(pt&&n&&Ws(e),e.flags|=1,It(t,e,a,c),e.child)}function Kd(t,e,a,n,u){if(en(e),e.stateNode===null){var c=Nn,o=a.contextType;typeof o=="object"&&o!==null&&(c=le(o)),c=new a(n,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=Mc,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=n,c.state=e.memoizedState,c.refs={},ic(e),o=a.contextType,c.context=typeof o=="object"&&o!==null?le(o):Nn,c.state=e.memoizedState,o=a.getDerivedStateFromProps,typeof o=="function"&&(xc(e,a,o,n),c.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(o=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),o!==c.state&&Mc.enqueueReplaceState(c,c.state,null),Nl(e,n,c,u),Ml(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){c=e.stateNode;var h=e.memoizedProps,y=ln(a,h);c.props=y;var E=c.context,x=a.contextType;o=Nn,typeof x=="object"&&x!==null&&(o=le(x));var z=a.getDerivedStateFromProps;x=typeof z=="function"||typeof c.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,x||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h||E!==o)&&zd(e,c,n,o),Aa=!1;var w=e.memoizedState;c.state=w,Nl(e,n,c,u),Ml(),E=e.memoizedState,h||w!==E||Aa?(typeof z=="function"&&(xc(e,a,z,n),E=e.memoizedState),(y=Aa||Cd(e,a,y,n,w,E,o))?(x||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=E),c.props=n,c.state=E,c.context=o,n=y):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{c=e.stateNode,sc(t,e),o=e.memoizedProps,x=ln(a,o),c.props=x,z=e.pendingProps,w=c.context,E=a.contextType,y=Nn,typeof E=="object"&&E!==null&&(y=le(E)),h=a.getDerivedStateFromProps,(E=typeof h=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(o!==z||w!==y)&&zd(e,c,n,y),Aa=!1,w=e.memoizedState,c.state=w,Nl(e,n,c,u),Ml();var T=e.memoizedState;o!==z||w!==T||Aa||t!==null&&t.dependencies!==null&&Zu(t.dependencies)?(typeof h=="function"&&(xc(e,a,h,n),T=e.memoizedState),(x=Aa||Cd(e,a,x,n,w,T,y)||t!==null&&t.dependencies!==null&&Zu(t.dependencies))?(E||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(n,T,y),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(n,T,y)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||o===t.memoizedProps&&w===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&w===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=T),c.props=n,c.state=T,c.context=y,n=x):(typeof c.componentDidUpdate!="function"||o===t.memoizedProps&&w===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&w===t.memoizedState||(e.flags|=1024),n=!1)}return c=n,ri(t,e),n=(e.flags&128)!==0,c||n?(c=e.stateNode,a=n&&typeof a.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&n?(e.child=Yn(e,t.child,null,u),e.child=Yn(e,null,a,u)):It(t,e,a,u),e.memoizedState=c.state,t=e.child):t=ca(t,e,u),t}function Jd(t,e,a,n){return Sl(),e.flags|=256,It(t,e,a,n),e.child}var Cc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function zc(t){return{baseLanes:t,cachePool:Bo()}}function Uc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=Be),t}function kd(t,e,a){var n=e.pendingProps,u=!1,c=(e.flags&128)!==0,o;if((o=c)||(o=t!==null&&t.memoizedState===null?!1:(Zt.current&2)!==0),o&&(u=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(pt){if(u?Oa(e):Ra(),pt){var h=Bt,y;if(y=h){t:{for(y=h,h=Ze;y.nodeType!==8;){if(!h){h=null;break t}if(y=Xe(y.nextSibling),y===null){h=null;break t}}h=y}h!==null?(e.memoizedState={dehydrated:h,treeContext:Wa!==null?{id:aa,overflow:na}:null,retryLane:536870912,hydrationErrors:null},y=pe(18,null,null,0),y.stateNode=h,y.return=e,e.child=y,se=e,Bt=null,y=!0):y=!1}y||Pa(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return _r(h)?e.lanes=32:e.lanes=536870912,null;sa(e)}return h=n.children,n=n.fallback,u?(Ra(),u=e.mode,h=fi({mode:"hidden",children:h},u),n=ka(n,u,a,null),h.return=e,n.return=e,h.sibling=n,e.child=h,u=e.child,u.memoizedState=zc(a),u.childLanes=Uc(t,o,a),e.memoizedState=Cc,n):(Oa(e),Bc(e,h))}if(y=t.memoizedState,y!==null&&(h=y.dehydrated,h!==null)){if(c)e.flags&256?(Oa(e),e.flags&=-257,e=Hc(t,e,a)):e.memoizedState!==null?(Ra(),e.child=t.child,e.flags|=128,e=null):(Ra(),u=n.fallback,h=e.mode,n=fi({mode:"visible",children:n.children},h),u=ka(u,h,a,null),u.flags|=2,n.return=e,u.return=e,n.sibling=u,e.child=n,Yn(e,t.child,null,a),n=e.child,n.memoizedState=zc(a),n.childLanes=Uc(t,o,a),e.memoizedState=Cc,e=u);else if(Oa(e),_r(h)){if(o=h.nextSibling&&h.nextSibling.dataset,o)var E=o.dgst;o=E,n=Error(s(419)),n.stack="",n.digest=o,Al({value:n,source:null,stack:null}),e=Hc(t,e,a)}else if(Jt||El(t,e,a,!1),o=(a&t.childLanes)!==0,Jt||o){if(o=Nt,o!==null&&(n=a&-a,n=(n&42)!==0?1:vs(n),n=(n&(o.suspendedLanes|a))!==0?0:n,n!==0&&n!==y.retryLane))throw y.retryLane=n,Mn(t,n),we(o,t,n),Yd;h.data==="$?"||tr(),e=Hc(t,e,a)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,Bt=Xe(h.nextSibling),se=e,pt=!0,Ia=null,Ze=!1,t!==null&&(Ce[ze++]=aa,Ce[ze++]=na,Ce[ze++]=Wa,aa=t.id,na=t.overflow,Wa=e),e=Bc(e,n.children),e.flags|=4096);return e}return u?(Ra(),u=n.fallback,h=e.mode,y=t.child,E=y.sibling,n=ea(y,{mode:"hidden",children:n.children}),n.subtreeFlags=y.subtreeFlags&65011712,E!==null?u=ea(E,u):(u=ka(u,h,a,null),u.flags|=2),u.return=e,n.return=e,n.sibling=u,e.child=n,n=u,u=e.child,h=t.child.memoizedState,h===null?h=zc(a):(y=h.cachePool,y!==null?(E=Qt._currentValue,y=y.parent!==E?{parent:E,pool:E}:y):y=Bo(),h={baseLanes:h.baseLanes|a,cachePool:y}),u.memoizedState=h,u.childLanes=Uc(t,o,a),e.memoizedState=Cc,n):(Oa(e),a=t.child,t=a.sibling,a=ea(a,{mode:"visible",children:n.children}),a.return=e,a.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=a,e.memoizedState=null,a)}function Bc(t,e){return e=fi({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function fi(t,e){return t=pe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Hc(t,e,a){return Yn(e,t.child,null,a),t=Bc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Wd(t,e,a){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),tc(t.return,e,a)}function qc(t,e,a,n,u){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:a,tailMode:u}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=n,c.tail=a,c.tailMode=u)}function Fd(t,e,a){var n=e.pendingProps,u=n.revealOrder,c=n.tail;if(It(t,e,n.children,a),n=Zt.current,(n&2)!==0)n=n&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Wd(t,a,e);else if(t.tag===19)Wd(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}n&=1}switch(j(Zt,n),u){case"forwards":for(a=e.child,u=null;a!==null;)t=a.alternate,t!==null&&ii(t)===null&&(u=a),a=a.sibling;a=u,a===null?(u=e.child,e.child=null):(u=a.sibling,a.sibling=null),qc(e,!1,u,a,c);break;case"backwards":for(a=null,u=e.child,e.child=null;u!==null;){if(t=u.alternate,t!==null&&ii(t)===null){e.child=u;break}t=u.sibling,u.sibling=a,a=u,u=t}qc(e,!0,a,null,c);break;case"together":qc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ca(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),Ca|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(El(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,a=ea(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=ea(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function jc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Zu(t)))}function $0(t,e,a){switch(e.tag){case 3:Dt(e,e.stateNode.containerInfo),Sa(e,Qt,t.memoizedState.cache),Sl();break;case 27:case 5:hs(e);break;case 4:Dt(e,e.stateNode.containerInfo);break;case 10:Sa(e,e.type,e.memoizedProps.value);break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(Oa(e),e.flags|=128,null):(a&e.child.childLanes)!==0?kd(t,e,a):(Oa(e),t=ca(t,e,a),t!==null?t.sibling:null);Oa(e);break;case 19:var u=(t.flags&128)!==0;if(n=(a&e.childLanes)!==0,n||(El(t,e,a,!1),n=(a&e.childLanes)!==0),u){if(n)return Fd(t,e,a);e.flags|=128}if(u=e.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),j(Zt,Zt.current),n)break;return null;case 22:case 23:return e.lanes=0,Zd(t,e,a);case 24:Sa(e,Qt,t.memoizedState.cache)}return ca(t,e,a)}function Id(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Jt=!0;else{if(!jc(t,a)&&(e.flags&128)===0)return Jt=!1,$0(t,e,a);Jt=(t.flags&131072)!==0}else Jt=!1,pt&&(e.flags&1048576)!==0&&xo(e,Qu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var n=e.elementType,u=n._init;if(n=u(n._payload),e.type=n,typeof n=="function")Ks(n)?(t=ln(n,t),e.tag=1,e=Kd(null,e,n,t,a)):(e.tag=0,e=Dc(null,e,n,t,a));else{if(n!=null){if(u=n.$$typeof,u===V){e.tag=11,e=Xd(null,e,n,t,a);break t}else if(u===tt){e.tag=14,e=Gd(null,e,n,t,a);break t}}throw e=Ie(n)||n,Error(s(306,e,""))}}return e;case 0:return Dc(t,e,e.type,e.pendingProps,a);case 1:return n=e.type,u=ln(n,e.pendingProps),Kd(t,e,n,u,a);case 3:t:{if(Dt(e,e.stateNode.containerInfo),t===null)throw Error(s(387));n=e.pendingProps;var c=e.memoizedState;u=c.element,sc(t,e),Nl(e,n,null,a);var o=e.memoizedState;if(n=o.cache,Sa(e,Qt,n),n!==c.cache&&ec(e,[Qt],a,!0),Ml(),n=o.element,c.isDehydrated)if(c={element:n,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=Jd(t,e,n,a);break t}else if(n!==u){u=Ne(Error(s(424)),e),Al(u),e=Jd(t,e,n,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Bt=Xe(t.firstChild),se=e,pt=!0,Ia=null,Ze=!0,a=Nd(e,null,n,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Sl(),n===u){e=ca(t,e,a);break t}It(t,e,n,a)}e=e.child}return e;case 26:return ri(t,e),t===null?(a=am(e.type,null,e.pendingProps,null))?e.memoizedState=a:pt||(a=e.type,t=e.pendingProps,n=wi(it.current).createElement(a),n[ne]=e,n[re]=t,te(n,a,t),Kt(n),e.stateNode=n):e.memoizedState=am(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return hs(e),t===null&&pt&&(n=e.stateNode=Ph(e.type,e.pendingProps,it.current),se=e,Ze=!0,u=Bt,Ha(e.type)?(vr=u,Bt=Xe(n.firstChild)):Bt=u),It(t,e,e.pendingProps.children,a),ri(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&pt&&((u=n=Bt)&&(n=b_(n,e.type,e.pendingProps,Ze),n!==null?(e.stateNode=n,se=e,Bt=Xe(n.firstChild),Ze=!1,u=!0):u=!1),u||Pa(e)),hs(e),u=e.type,c=e.pendingProps,o=t!==null?t.memoizedProps:null,n=c.children,mr(u,c)?n=null:o!==null&&mr(u,o)&&(e.flags|=32),e.memoizedState!==null&&(u=hc(t,e,j0,null,null,a),Il._currentValue=u),ri(t,e),It(t,e,n,a),e.child;case 6:return t===null&&pt&&((t=a=Bt)&&(a=S_(a,e.pendingProps,Ze),a!==null?(e.stateNode=a,se=e,Bt=null,t=!0):t=!1),t||Pa(e)),null;case 13:return kd(t,e,a);case 4:return Dt(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=Yn(e,null,n,a):It(t,e,n,a),e.child;case 11:return Xd(t,e,e.type,e.pendingProps,a);case 7:return It(t,e,e.pendingProps,a),e.child;case 8:return It(t,e,e.pendingProps.children,a),e.child;case 12:return It(t,e,e.pendingProps.children,a),e.child;case 10:return n=e.pendingProps,Sa(e,e.type,n.value),It(t,e,n.children,a),e.child;case 9:return u=e.type._context,n=e.pendingProps.children,en(e),u=le(u),n=n(u),e.flags|=1,It(t,e,n,a),e.child;case 14:return Gd(t,e,e.type,e.pendingProps,a);case 15:return Qd(t,e,e.type,e.pendingProps,a);case 19:return Fd(t,e,a);case 31:return n=e.pendingProps,a=e.mode,n={mode:n.mode,children:n.children},t===null?(a=fi(n,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=ea(t.child,n),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return Zd(t,e,a);case 24:return en(e),n=le(Qt),t===null?(u=lc(),u===null&&(u=Nt,c=ac(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=a),u=c),e.memoizedState={parent:n,cache:u},ic(e),Sa(e,Qt,u)):((t.lanes&a)!==0&&(sc(t,e),Nl(e,null,null,a),Ml()),u=t.memoizedState,c=e.memoizedState,u.parent!==n?(u={parent:n,cache:n},e.memoizedState=u,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=u),Sa(e,Qt,n)):(n=c.cache,Sa(e,Qt,n),n!==u.cache&&ec(e,[Qt],a,!0))),It(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function ra(t){t.flags|=4}function Pd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!sm(e)){if(e=Ue.current,e!==null&&((yt&4194048)===yt?Ve!==null:(yt&62914560)!==yt&&(yt&536870912)===0||e!==Ve))throw Rl=uc,Ho;t.flags|=8192}}function oi(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Nf():536870912,t.lanes|=e,Zn|=e)}function ql(t,e){if(!pt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var n=null;a!==null;)a.alternate!==null&&(n=a),a=a.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function Ut(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,n=0;if(e)for(var u=t.child;u!==null;)a|=u.lanes|u.childLanes,n|=u.subtreeFlags&65011712,n|=u.flags&65011712,u.return=t,u=u.sibling;else for(u=t.child;u!==null;)a|=u.lanes|u.childLanes,n|=u.subtreeFlags,n|=u.flags,u.return=t,u=u.sibling;return t.subtreeFlags|=n,t.childLanes=a,e}function K0(t,e,a){var n=e.pendingProps;switch(Fs(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ut(e),null;case 1:return Ut(e),null;case 3:return a=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),ua(Qt),_a(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(bl(e)?ra(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Do())),Ut(e),null;case 26:return a=e.memoizedState,t===null?(ra(e),a!==null?(Ut(e),Pd(e,a)):(Ut(e),e.flags&=-16777217)):a?a!==t.memoizedState?(ra(e),Ut(e),Pd(e,a)):(Ut(e),e.flags&=-16777217):(t.memoizedProps!==n&&ra(e),Ut(e),e.flags&=-16777217),null;case 27:Au(e),a=it.current;var u=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==n&&ra(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Ut(e),null}t=F.current,bl(e)?Mo(e):(t=Ph(u,n,a),e.stateNode=t,ra(e))}return Ut(e),null;case 5:if(Au(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&ra(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Ut(e),null}if(t=F.current,bl(e))Mo(e);else{switch(u=wi(it.current),t){case 1:t=u.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=u.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=u.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=u.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=u.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof n.is=="string"?u.createElement("select",{is:n.is}):u.createElement("select"),n.multiple?t.multiple=!0:n.size&&(t.size=n.size);break;default:t=typeof n.is=="string"?u.createElement(a,{is:n.is}):u.createElement(a)}}t[ne]=e,t[re]=n;t:for(u=e.child;u!==null;){if(u.tag===5||u.tag===6)t.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===e)break t;for(;u.sibling===null;){if(u.return===null||u.return===e)break t;u=u.return}u.sibling.return=u.return,u=u.sibling}e.stateNode=t;t:switch(te(t,a,n),a){case"button":case"input":case"select":case"textarea":t=!!n.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&ra(e)}}return Ut(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&ra(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(s(166));if(t=it.current,bl(e)){if(t=e.stateNode,a=e.memoizedProps,n=null,u=se,u!==null)switch(u.tag){case 27:case 5:n=u.memoizedProps}t[ne]=e,t=!!(t.nodeValue===a||n!==null&&n.suppressHydrationWarning===!0||$h(t.nodeValue,a)),t||Pa(e)}else t=wi(t).createTextNode(n),t[ne]=e,e.stateNode=t}return Ut(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(u=bl(e),n!==null&&n.dehydrated!==null){if(t===null){if(!u)throw Error(s(318));if(u=e.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[ne]=e}else Sl(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ut(e),u=!1}else u=Do(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=u),u=!0;if(!u)return e.flags&256?(sa(e),e):(sa(e),null)}if(sa(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=n!==null,t=t!==null&&t.memoizedState!==null,a){n=e.child,u=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(u=n.alternate.memoizedState.cachePool.pool);var c=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(c=n.memoizedState.cachePool.pool),c!==u&&(n.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),oi(e,e.updateQueue),Ut(e),null;case 4:return _a(),t===null&&rr(e.stateNode.containerInfo),Ut(e),null;case 10:return ua(e.type),Ut(e),null;case 19:if(X(Zt),u=e.memoizedState,u===null)return Ut(e),null;if(n=(e.flags&128)!==0,c=u.rendering,c===null)if(n)ql(u,!1);else{if(Ht!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=ii(t),c!==null){for(e.flags|=128,ql(u,!1),t=c.updateQueue,e.updateQueue=t,oi(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)Ro(a,t),a=a.sibling;return j(Zt,Zt.current&1|2),e.child}t=t.sibling}u.tail!==null&&Qe()>mi&&(e.flags|=128,n=!0,ql(u,!1),e.lanes=4194304)}else{if(!n)if(t=ii(c),t!==null){if(e.flags|=128,n=!0,t=t.updateQueue,e.updateQueue=t,oi(e,t),ql(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!pt)return Ut(e),null}else 2*Qe()-u.renderingStartTime>mi&&a!==536870912&&(e.flags|=128,n=!0,ql(u,!1),e.lanes=4194304);u.isBackwards?(c.sibling=e.child,e.child=c):(t=u.last,t!==null?t.sibling=c:e.child=c,u.last=c)}return u.tail!==null?(e=u.tail,u.rendering=e,u.tail=e.sibling,u.renderingStartTime=Qe(),e.sibling=null,t=Zt.current,j(Zt,n?t&1|2:t&1),e):(Ut(e),null);case 22:case 23:return sa(e),oc(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?(a&536870912)!==0&&(e.flags&128)===0&&(Ut(e),e.subtreeFlags&6&&(e.flags|=8192)):Ut(e),a=e.updateQueue,a!==null&&oi(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==a&&(e.flags|=2048),t!==null&&X(an),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ua(Qt),Ut(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function J0(t,e){switch(Fs(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ua(Qt),_a(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Au(e),null;case 13:if(sa(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));Sl()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return X(Zt),null;case 4:return _a(),null;case 10:return ua(e.type),null;case 22:case 23:return sa(e),oc(),t!==null&&X(an),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ua(Qt),null;case 25:return null;default:return null}}function th(t,e){switch(Fs(e),e.tag){case 3:ua(Qt),_a();break;case 26:case 27:case 5:Au(e);break;case 4:_a();break;case 13:sa(e);break;case 19:X(Zt);break;case 10:ua(e.type);break;case 22:case 23:sa(e),oc(),t!==null&&X(an);break;case 24:ua(Qt)}}function jl(t,e){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){n=void 0;var c=a.create,o=a.inst;n=c(),o.destroy=n}a=a.next}while(a!==u)}}catch(h){Mt(e,e.return,h)}}function xa(t,e,a){try{var n=e.updateQueue,u=n!==null?n.lastEffect:null;if(u!==null){var c=u.next;n=c;do{if((n.tag&t)===t){var o=n.inst,h=o.destroy;if(h!==void 0){o.destroy=void 0,u=e;var y=a,E=h;try{E()}catch(x){Mt(u,y,x)}}}n=n.next}while(n!==c)}}catch(x){Mt(e,e.return,x)}}function eh(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{Go(e,a)}catch(n){Mt(t,t.return,n)}}}function ah(t,e,a){a.props=ln(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(n){Mt(t,e,n)}}function Ll(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof a=="function"?t.refCleanup=a(n):a.current=n}}catch(u){Mt(t,e,u)}}function $e(t,e){var a=t.ref,n=t.refCleanup;if(a!==null)if(typeof n=="function")try{n()}catch(u){Mt(t,e,u)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(u){Mt(t,e,u)}else a.current=null}function nh(t){var e=t.type,a=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break t;case"img":a.src?n.src=a.src:a.srcSet&&(n.srcset=a.srcSet)}}catch(u){Mt(t,t.return,u)}}function Lc(t,e,a){try{var n=t.stateNode;y_(n,t.type,a,e),n[re]=e}catch(u){Mt(t,t.return,u)}}function lh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Ha(t.type)||t.tag===4}function Yc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||lh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Ha(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Xc(t,e,a){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Ei));else if(n!==4&&(n===27&&Ha(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(Xc(t,e,a),t=t.sibling;t!==null;)Xc(t,e,a),t=t.sibling}function di(t,e,a){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(n!==4&&(n===27&&Ha(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(di(t,e,a),t=t.sibling;t!==null;)di(t,e,a),t=t.sibling}function uh(t){var e=t.stateNode,a=t.memoizedProps;try{for(var n=t.type,u=e.attributes;u.length;)e.removeAttributeNode(u[0]);te(e,n,a),e[ne]=t,e[re]=a}catch(c){Mt(t,t.return,c)}}var fa=!1,jt=!1,Gc=!1,ih=typeof WeakSet=="function"?WeakSet:Set,kt=null;function k0(t,e){if(t=t.containerInfo,dr=Ni,t=_o(t),Ys(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var n=a.getSelection&&a.getSelection();if(n&&n.rangeCount!==0){a=n.anchorNode;var u=n.anchorOffset,c=n.focusNode;n=n.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break t}var o=0,h=-1,y=-1,E=0,x=0,z=t,w=null;e:for(;;){for(var T;z!==a||u!==0&&z.nodeType!==3||(h=o+u),z!==c||n!==0&&z.nodeType!==3||(y=o+n),z.nodeType===3&&(o+=z.nodeValue.length),(T=z.firstChild)!==null;)w=z,z=T;for(;;){if(z===t)break e;if(w===a&&++E===u&&(h=o),w===c&&++x===n&&(y=o),(T=z.nextSibling)!==null)break;z=w,w=z.parentNode}z=T}a=h===-1||y===-1?null:{start:h,end:y}}else a=null}a=a||{start:0,end:0}}else a=null;for(hr={focusedElem:t,selectionRange:a},Ni=!1,kt=e;kt!==null;)if(e=kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,kt=t;else for(;kt!==null;){switch(e=kt,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,a=e,u=c.memoizedProps,c=c.memoizedState,n=a.stateNode;try{var at=ln(a.type,u,a.elementType===a.type);t=n.getSnapshotBeforeUpdate(at,c),n.__reactInternalSnapshotBeforeUpdate=t}catch(I){Mt(a,a.return,I)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)gr(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":gr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,kt=t;break}kt=e.return}}function sh(t,e,a){var n=a.flags;switch(a.tag){case 0:case 11:case 15:Ma(t,a),n&4&&jl(5,a);break;case 1:if(Ma(t,a),n&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(o){Mt(a,a.return,o)}else{var u=ln(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(u,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){Mt(a,a.return,o)}}n&64&&eh(a),n&512&&Ll(a,a.return);break;case 3:if(Ma(t,a),n&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{Go(t,e)}catch(o){Mt(a,a.return,o)}}break;case 27:e===null&&n&4&&uh(a);case 26:case 5:Ma(t,a),e===null&&n&4&&nh(a),n&512&&Ll(a,a.return);break;case 12:Ma(t,a);break;case 13:Ma(t,a),n&4&&fh(t,a),n&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=l_.bind(null,a),A_(t,a))));break;case 22:if(n=a.memoizedState!==null||fa,!n){e=e!==null&&e.memoizedState!==null||jt,u=fa;var c=jt;fa=n,(jt=e)&&!c?Na(t,a,(a.subtreeFlags&8772)!==0):Ma(t,a),fa=u,jt=c}break;case 30:break;default:Ma(t,a)}}function ch(t){var e=t.alternate;e!==null&&(t.alternate=null,ch(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ss(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ct=null,de=!1;function oa(t,e,a){for(a=a.child;a!==null;)rh(t,e,a),a=a.sibling}function rh(t,e,a){if(ge&&typeof ge.onCommitFiberUnmount=="function")try{ge.onCommitFiberUnmount(il,a)}catch{}switch(a.tag){case 26:jt||$e(a,e),oa(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:jt||$e(a,e);var n=Ct,u=de;Ha(a.type)&&(Ct=a.stateNode,de=!1),oa(t,e,a),Jl(a.stateNode),Ct=n,de=u;break;case 5:jt||$e(a,e);case 6:if(n=Ct,u=de,Ct=null,oa(t,e,a),Ct=n,de=u,Ct!==null)if(de)try{(Ct.nodeType===9?Ct.body:Ct.nodeName==="HTML"?Ct.ownerDocument.body:Ct).removeChild(a.stateNode)}catch(c){Mt(a,e,c)}else try{Ct.removeChild(a.stateNode)}catch(c){Mt(a,e,c)}break;case 18:Ct!==null&&(de?(t=Ct,Fh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),au(t)):Fh(Ct,a.stateNode));break;case 4:n=Ct,u=de,Ct=a.stateNode.containerInfo,de=!0,oa(t,e,a),Ct=n,de=u;break;case 0:case 11:case 14:case 15:jt||xa(2,a,e),jt||xa(4,a,e),oa(t,e,a);break;case 1:jt||($e(a,e),n=a.stateNode,typeof n.componentWillUnmount=="function"&&ah(a,e,n)),oa(t,e,a);break;case 21:oa(t,e,a);break;case 22:jt=(n=jt)||a.memoizedState!==null,oa(t,e,a),jt=n;break;default:oa(t,e,a)}}function fh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{au(t)}catch(a){Mt(e,e.return,a)}}function W0(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new ih),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new ih),e;default:throw Error(s(435,t.tag))}}function Qc(t,e){var a=W0(t);e.forEach(function(n){var u=u_.bind(null,t,n);a.has(n)||(a.add(n),n.then(u,u))})}function be(t,e){var a=e.deletions;if(a!==null)for(var n=0;n<a.length;n++){var u=a[n],c=t,o=e,h=o;t:for(;h!==null;){switch(h.tag){case 27:if(Ha(h.type)){Ct=h.stateNode,de=!1;break t}break;case 5:Ct=h.stateNode,de=!1;break t;case 3:case 4:Ct=h.stateNode.containerInfo,de=!0;break t}h=h.return}if(Ct===null)throw Error(s(160));rh(c,o,u),Ct=null,de=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)oh(e,t),e=e.sibling}var Ye=null;function oh(t,e){var a=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:be(e,t),Se(t),n&4&&(xa(3,t,t.return),jl(3,t),xa(5,t,t.return));break;case 1:be(e,t),Se(t),n&512&&(jt||a===null||$e(a,a.return)),n&64&&fa&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?n:a.concat(n))));break;case 26:var u=Ye;if(be(e,t),Se(t),n&512&&(jt||a===null||$e(a,a.return)),n&4){var c=a!==null?a.memoizedState:null;if(n=t.memoizedState,a===null)if(n===null)if(t.stateNode===null){t:{n=t.type,a=t.memoizedProps,u=u.ownerDocument||u;e:switch(n){case"title":c=u.getElementsByTagName("title")[0],(!c||c[rl]||c[ne]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(n),u.head.insertBefore(c,u.querySelector("head > title"))),te(c,n,a),c[ne]=t,Kt(c),n=c;break t;case"link":var o=um("link","href",u).get(n+(a.href||""));if(o){for(var h=0;h<o.length;h++)if(c=o[h],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){o.splice(h,1);break e}}c=u.createElement(n),te(c,n,a),u.head.appendChild(c);break;case"meta":if(o=um("meta","content",u).get(n+(a.content||""))){for(h=0;h<o.length;h++)if(c=o[h],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){o.splice(h,1);break e}}c=u.createElement(n),te(c,n,a),u.head.appendChild(c);break;default:throw Error(s(468,n))}c[ne]=t,Kt(c),n=c}t.stateNode=n}else im(u,t.type,t.stateNode);else t.stateNode=lm(u,n,t.memoizedProps);else c!==n?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,n===null?im(u,t.type,t.stateNode):lm(u,n,t.memoizedProps)):n===null&&t.stateNode!==null&&Lc(t,t.memoizedProps,a.memoizedProps)}break;case 27:be(e,t),Se(t),n&512&&(jt||a===null||$e(a,a.return)),a!==null&&n&4&&Lc(t,t.memoizedProps,a.memoizedProps);break;case 5:if(be(e,t),Se(t),n&512&&(jt||a===null||$e(a,a.return)),t.flags&32){u=t.stateNode;try{An(u,"")}catch(T){Mt(t,t.return,T)}}n&4&&t.stateNode!=null&&(u=t.memoizedProps,Lc(t,u,a!==null?a.memoizedProps:u)),n&1024&&(Gc=!0);break;case 6:if(be(e,t),Se(t),n&4){if(t.stateNode===null)throw Error(s(162));n=t.memoizedProps,a=t.stateNode;try{a.nodeValue=n}catch(T){Mt(t,t.return,T)}}break;case 3:if(Ri=null,u=Ye,Ye=Ti(e.containerInfo),be(e,t),Ye=u,Se(t),n&4&&a!==null&&a.memoizedState.isDehydrated)try{au(e.containerInfo)}catch(T){Mt(t,t.return,T)}Gc&&(Gc=!1,dh(t));break;case 4:n=Ye,Ye=Ti(t.stateNode.containerInfo),be(e,t),Se(t),Ye=n;break;case 12:be(e,t),Se(t);break;case 13:be(e,t),Se(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(kc=Qe()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,Qc(t,n)));break;case 22:u=t.memoizedState!==null;var y=a!==null&&a.memoizedState!==null,E=fa,x=jt;if(fa=E||u,jt=x||y,be(e,t),jt=x,fa=E,Se(t),n&8192)t:for(e=t.stateNode,e._visibility=u?e._visibility&-2:e._visibility|1,u&&(a===null||y||fa||jt||un(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){y=a=e;try{if(c=y.stateNode,u)o=c.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{h=y.stateNode;var z=y.memoizedProps.style,w=z!=null&&z.hasOwnProperty("display")?z.display:null;h.style.display=w==null||typeof w=="boolean"?"":(""+w).trim()}}catch(T){Mt(y,y.return,T)}}}else if(e.tag===6){if(a===null){y=e;try{y.stateNode.nodeValue=u?"":y.memoizedProps}catch(T){Mt(y,y.return,T)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(a=n.retryQueue,a!==null&&(n.retryQueue=null,Qc(t,a))));break;case 19:be(e,t),Se(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,Qc(t,n)));break;case 30:break;case 21:break;default:be(e,t),Se(t)}}function Se(t){var e=t.flags;if(e&2){try{for(var a,n=t.return;n!==null;){if(lh(n)){a=n;break}n=n.return}if(a==null)throw Error(s(160));switch(a.tag){case 27:var u=a.stateNode,c=Yc(t);di(t,c,u);break;case 5:var o=a.stateNode;a.flags&32&&(An(o,""),a.flags&=-33);var h=Yc(t);di(t,h,o);break;case 3:case 4:var y=a.stateNode.containerInfo,E=Yc(t);Xc(t,E,y);break;default:throw Error(s(161))}}catch(x){Mt(t,t.return,x)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function dh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;dh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ma(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)sh(t,e.alternate,e),e=e.sibling}function un(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:xa(4,e,e.return),un(e);break;case 1:$e(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&ah(e,e.return,a),un(e);break;case 27:Jl(e.stateNode);case 26:case 5:$e(e,e.return),un(e);break;case 22:e.memoizedState===null&&un(e);break;case 30:un(e);break;default:un(e)}t=t.sibling}}function Na(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,u=t,c=e,o=c.flags;switch(c.tag){case 0:case 11:case 15:Na(u,c,a),jl(4,c);break;case 1:if(Na(u,c,a),n=c,u=n.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(E){Mt(n,n.return,E)}if(n=c,u=n.updateQueue,u!==null){var h=n.stateNode;try{var y=u.shared.hiddenCallbacks;if(y!==null)for(u.shared.hiddenCallbacks=null,u=0;u<y.length;u++)Xo(y[u],h)}catch(E){Mt(n,n.return,E)}}a&&o&64&&eh(c),Ll(c,c.return);break;case 27:uh(c);case 26:case 5:Na(u,c,a),a&&n===null&&o&4&&nh(c),Ll(c,c.return);break;case 12:Na(u,c,a);break;case 13:Na(u,c,a),a&&o&4&&fh(u,c);break;case 22:c.memoizedState===null&&Na(u,c,a),Ll(c,c.return);break;case 30:break;default:Na(u,c,a)}e=e.sibling}}function Zc(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&wl(a))}function Vc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&wl(t))}function Ke(t,e,a,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)hh(t,e,a,n),e=e.sibling}function hh(t,e,a,n){var u=e.flags;switch(e.tag){case 0:case 11:case 15:Ke(t,e,a,n),u&2048&&jl(9,e);break;case 1:Ke(t,e,a,n);break;case 3:Ke(t,e,a,n),u&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&wl(t)));break;case 12:if(u&2048){Ke(t,e,a,n),t=e.stateNode;try{var c=e.memoizedProps,o=c.id,h=c.onPostCommit;typeof h=="function"&&h(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){Mt(e,e.return,y)}}else Ke(t,e,a,n);break;case 13:Ke(t,e,a,n);break;case 23:break;case 22:c=e.stateNode,o=e.alternate,e.memoizedState!==null?c._visibility&2?Ke(t,e,a,n):Yl(t,e):c._visibility&2?Ke(t,e,a,n):(c._visibility|=2,Xn(t,e,a,n,(e.subtreeFlags&10256)!==0)),u&2048&&Zc(o,e);break;case 24:Ke(t,e,a,n),u&2048&&Vc(e.alternate,e);break;default:Ke(t,e,a,n)}}function Xn(t,e,a,n,u){for(u=u&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,o=e,h=a,y=n,E=o.flags;switch(o.tag){case 0:case 11:case 15:Xn(c,o,h,y,u),jl(8,o);break;case 23:break;case 22:var x=o.stateNode;o.memoizedState!==null?x._visibility&2?Xn(c,o,h,y,u):Yl(c,o):(x._visibility|=2,Xn(c,o,h,y,u)),u&&E&2048&&Zc(o.alternate,o);break;case 24:Xn(c,o,h,y,u),u&&E&2048&&Vc(o.alternate,o);break;default:Xn(c,o,h,y,u)}e=e.sibling}}function Yl(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,n=e,u=n.flags;switch(n.tag){case 22:Yl(a,n),u&2048&&Zc(n.alternate,n);break;case 24:Yl(a,n),u&2048&&Vc(n.alternate,n);break;default:Yl(a,n)}e=e.sibling}}var Xl=8192;function Gn(t){if(t.subtreeFlags&Xl)for(t=t.child;t!==null;)mh(t),t=t.sibling}function mh(t){switch(t.tag){case 26:Gn(t),t.flags&Xl&&t.memoizedState!==null&&B_(Ye,t.memoizedState,t.memoizedProps);break;case 5:Gn(t);break;case 3:case 4:var e=Ye;Ye=Ti(t.stateNode.containerInfo),Gn(t),Ye=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Xl,Xl=16777216,Gn(t),Xl=e):Gn(t));break;default:Gn(t)}}function yh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Gl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var n=e[a];kt=n,_h(n,t)}yh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)gh(t),t=t.sibling}function gh(t){switch(t.tag){case 0:case 11:case 15:Gl(t),t.flags&2048&&xa(9,t,t.return);break;case 3:Gl(t);break;case 12:Gl(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,hi(t)):Gl(t);break;default:Gl(t)}}function hi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var n=e[a];kt=n,_h(n,t)}yh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:xa(8,e,e.return),hi(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,hi(e));break;default:hi(e)}t=t.sibling}}function _h(t,e){for(;kt!==null;){var a=kt;switch(a.tag){case 0:case 11:case 15:xa(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var n=a.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:wl(a.memoizedState.cache)}if(n=a.child,n!==null)n.return=a,kt=n;else t:for(a=t;kt!==null;){n=kt;var u=n.sibling,c=n.return;if(ch(n),n===a){kt=null;break t}if(u!==null){u.return=c,kt=u;break t}kt=c}}}var F0={getCacheForType:function(t){var e=le(Qt),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},I0=typeof WeakMap=="function"?WeakMap:Map,St=0,Nt=null,dt=null,yt=0,At=0,Ae=null,Da=!1,Qn=!1,$c=!1,da=0,Ht=0,Ca=0,sn=0,Kc=0,Be=0,Zn=0,Ql=null,he=null,Jc=!1,kc=0,mi=1/0,yi=null,za=null,Pt=0,Ua=null,Vn=null,$n=0,Wc=0,Fc=null,vh=null,Zl=0,Ic=null;function Ee(){if((St&2)!==0&&yt!==0)return yt&-yt;if(N.T!==null){var t=zn;return t!==0?t:ur()}return zf()}function ph(){Be===0&&(Be=(yt&536870912)===0||pt?Mf():536870912);var t=Ue.current;return t!==null&&(t.flags|=32),Be}function we(t,e,a){(t===Nt&&(At===2||At===9)||t.cancelPendingCommit!==null)&&(Kn(t,0),Ba(t,yt,Be,!1)),cl(t,a),((St&2)===0||t!==Nt)&&(t===Nt&&((St&2)===0&&(sn|=a),Ht===4&&Ba(t,yt,Be,!1)),Je(t))}function bh(t,e,a){if((St&6)!==0)throw Error(s(327));var n=!a&&(e&124)===0&&(e&t.expiredLanes)===0||sl(t,e),u=n?e_(t,e):er(t,e,!0),c=n;do{if(u===0){Qn&&!n&&Ba(t,e,0,!1);break}else{if(a=t.current.alternate,c&&!P0(a)){u=er(t,e,!1),c=!1;continue}if(u===2){if(c=e,t.errorRecoveryDisabledLanes&c)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var h=t;u=Ql;var y=h.current.memoizedState.isDehydrated;if(y&&(Kn(h,o).flags|=256),o=er(h,o,!1),o!==2){if($c&&!y){h.errorRecoveryDisabledLanes|=c,sn|=c,u=4;break t}c=he,he=u,c!==null&&(he===null?he=c:he.push.apply(he,c))}u=o}if(c=!1,u!==2)continue}}if(u===1){Kn(t,0),Ba(t,e,0,!0);break}t:{switch(n=t,c=u,c){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:Ba(n,e,Be,!Da);break t;case 2:he=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(u=kc+300-Qe(),10<u)){if(Ba(n,e,Be,!Da),Ou(n,0,!0)!==0)break t;n.timeoutHandle=kh(Sh.bind(null,n,a,he,yi,Jc,e,Be,sn,Zn,Da,c,2,-0,0),u);break t}Sh(n,a,he,yi,Jc,e,Be,sn,Zn,Da,c,0,-0,0)}}break}while(!0);Je(t)}function Sh(t,e,a,n,u,c,o,h,y,E,x,z,w,T){if(t.timeoutHandle=-1,z=e.subtreeFlags,(z&8192||(z&16785408)===16785408)&&(Fl={stylesheets:null,count:0,unsuspend:U_},mh(e),z=H_(),z!==null)){t.cancelPendingCommit=z(xh.bind(null,t,e,c,a,n,u,o,h,y,x,1,w,T)),Ba(t,c,o,!E);return}xh(t,e,c,a,n,u,o,h,y)}function P0(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var n=0;n<a.length;n++){var u=a[n],c=u.getSnapshot;u=u.value;try{if(!ve(c(),u))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Ba(t,e,a,n){e&=~Kc,e&=~sn,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var u=e;0<u;){var c=31-_e(u),o=1<<c;n[c]=-1,u&=~o}a!==0&&Df(t,a,e)}function gi(){return(St&6)===0?(Vl(0),!1):!0}function Pc(){if(dt!==null){if(At===0)var t=dt.return;else t=dt,la=tn=null,gc(t),Ln=null,Bl=0,t=dt;for(;t!==null;)th(t.alternate,t),t=t.return;dt=null}}function Kn(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,__(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),Pc(),Nt=t,dt=a=ea(t.current,null),yt=e,At=0,Ae=null,Da=!1,Qn=sl(t,e),$c=!1,Zn=Be=Kc=sn=Ca=Ht=0,he=Ql=null,Jc=!1,(e&8)!==0&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var u=31-_e(n),c=1<<u;e|=t[u],n&=~c}return da=e,ju(),a}function Ah(t,e){rt=null,N.H=ni,e===Ol||e===Ku?(e=Lo(),At=3):e===Ho?(e=Lo(),At=4):At=e===Yd?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ae=e,dt===null&&(Ht=1,ci(t,Ne(e,t.current)))}function Eh(){var t=N.H;return N.H=ni,t===null?ni:t}function wh(){var t=N.A;return N.A=F0,t}function tr(){Ht=4,Da||(yt&4194048)!==yt&&Ue.current!==null||(Qn=!0),(Ca&134217727)===0&&(sn&134217727)===0||Nt===null||Ba(Nt,yt,Be,!1)}function er(t,e,a){var n=St;St|=2;var u=Eh(),c=wh();(Nt!==t||yt!==e)&&(yi=null,Kn(t,e)),e=!1;var o=Ht;t:do try{if(At!==0&&dt!==null){var h=dt,y=Ae;switch(At){case 8:Pc(),o=6;break t;case 3:case 2:case 9:case 6:Ue.current===null&&(e=!0);var E=At;if(At=0,Ae=null,Jn(t,h,y,E),a&&Qn){o=0;break t}break;default:E=At,At=0,Ae=null,Jn(t,h,y,E)}}t_(),o=Ht;break}catch(x){Ah(t,x)}while(!0);return e&&t.shellSuspendCounter++,la=tn=null,St=n,N.H=u,N.A=c,dt===null&&(Nt=null,yt=0,ju()),o}function t_(){for(;dt!==null;)Th(dt)}function e_(t,e){var a=St;St|=2;var n=Eh(),u=wh();Nt!==t||yt!==e?(yi=null,mi=Qe()+500,Kn(t,e)):Qn=sl(t,e);t:do try{if(At!==0&&dt!==null){e=dt;var c=Ae;e:switch(At){case 1:At=0,Ae=null,Jn(t,e,c,1);break;case 2:case 9:if(qo(c)){At=0,Ae=null,Oh(e);break}e=function(){At!==2&&At!==9||Nt!==t||(At=7),Je(t)},c.then(e,e);break t;case 3:At=7;break t;case 4:At=5;break t;case 7:qo(c)?(At=0,Ae=null,Oh(e)):(At=0,Ae=null,Jn(t,e,c,7));break;case 5:var o=null;switch(dt.tag){case 26:o=dt.memoizedState;case 5:case 27:var h=dt;if(!o||sm(o)){At=0,Ae=null;var y=h.sibling;if(y!==null)dt=y;else{var E=h.return;E!==null?(dt=E,_i(E)):dt=null}break e}}At=0,Ae=null,Jn(t,e,c,5);break;case 6:At=0,Ae=null,Jn(t,e,c,6);break;case 8:Pc(),Ht=6;break t;default:throw Error(s(462))}}a_();break}catch(x){Ah(t,x)}while(!0);return la=tn=null,N.H=n,N.A=u,St=a,dt!==null?0:(Nt=null,yt=0,ju(),Ht)}function a_(){for(;dt!==null&&!wg();)Th(dt)}function Th(t){var e=Id(t.alternate,t,da);t.memoizedProps=t.pendingProps,e===null?_i(t):dt=e}function Oh(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=$d(a,e,e.pendingProps,e.type,void 0,yt);break;case 11:e=$d(a,e,e.pendingProps,e.type.render,e.ref,yt);break;case 5:gc(e);default:th(a,e),e=dt=Ro(e,da),e=Id(a,e,da)}t.memoizedProps=t.pendingProps,e===null?_i(t):dt=e}function Jn(t,e,a,n){la=tn=null,gc(e),Ln=null,Bl=0;var u=e.return;try{if(V0(t,u,e,a,yt)){Ht=1,ci(t,Ne(a,t.current)),dt=null;return}}catch(c){if(u!==null)throw dt=u,c;Ht=1,ci(t,Ne(a,t.current)),dt=null;return}e.flags&32768?(pt||n===1?t=!0:Qn||(yt&536870912)!==0?t=!1:(Da=t=!0,(n===2||n===9||n===3||n===6)&&(n=Ue.current,n!==null&&n.tag===13&&(n.flags|=16384))),Rh(e,t)):_i(e)}function _i(t){var e=t;do{if((e.flags&32768)!==0){Rh(e,Da);return}t=e.return;var a=K0(e.alternate,e,da);if(a!==null){dt=a;return}if(e=e.sibling,e!==null){dt=e;return}dt=e=t}while(e!==null);Ht===0&&(Ht=5)}function Rh(t,e){do{var a=J0(t.alternate,t);if(a!==null){a.flags&=32767,dt=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){dt=t;return}dt=t=a}while(t!==null);Ht=6,dt=null}function xh(t,e,a,n,u,c,o,h,y){t.cancelPendingCommit=null;do vi();while(Pt!==0);if((St&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(c=e.lanes|e.childLanes,c|=Vs,Ug(t,a,c,o,h,y),t===Nt&&(dt=Nt=null,yt=0),Vn=e,Ua=t,$n=a,Wc=c,Fc=u,vh=n,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,i_(Eu,function(){return zh(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||n){n=N.T,N.T=null,u=Y.p,Y.p=2,o=St,St|=4;try{k0(t,e,a)}finally{St=o,Y.p=u,N.T=n}}Pt=1,Mh(),Nh(),Dh()}}function Mh(){if(Pt===1){Pt=0;var t=Ua,e=Vn,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=N.T,N.T=null;var n=Y.p;Y.p=2;var u=St;St|=4;try{oh(e,t);var c=hr,o=_o(t.containerInfo),h=c.focusedElem,y=c.selectionRange;if(o!==h&&h&&h.ownerDocument&&go(h.ownerDocument.documentElement,h)){if(y!==null&&Ys(h)){var E=y.start,x=y.end;if(x===void 0&&(x=E),"selectionStart"in h)h.selectionStart=E,h.selectionEnd=Math.min(x,h.value.length);else{var z=h.ownerDocument||document,w=z&&z.defaultView||window;if(w.getSelection){var T=w.getSelection(),at=h.textContent.length,I=Math.min(y.start,at),Ot=y.end===void 0?I:Math.min(y.end,at);!T.extend&&I>Ot&&(o=Ot,Ot=I,I=o);var p=yo(h,I),_=yo(h,Ot);if(p&&_&&(T.rangeCount!==1||T.anchorNode!==p.node||T.anchorOffset!==p.offset||T.focusNode!==_.node||T.focusOffset!==_.offset)){var S=z.createRange();S.setStart(p.node,p.offset),T.removeAllRanges(),I>Ot?(T.addRange(S),T.extend(_.node,_.offset)):(S.setEnd(_.node,_.offset),T.addRange(S))}}}}for(z=[],T=h;T=T.parentNode;)T.nodeType===1&&z.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<z.length;h++){var D=z[h];D.element.scrollLeft=D.left,D.element.scrollTop=D.top}}Ni=!!dr,hr=dr=null}finally{St=u,Y.p=n,N.T=a}}t.current=e,Pt=2}}function Nh(){if(Pt===2){Pt=0;var t=Ua,e=Vn,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=N.T,N.T=null;var n=Y.p;Y.p=2;var u=St;St|=4;try{sh(t,e.alternate,e)}finally{St=u,Y.p=n,N.T=a}}Pt=3}}function Dh(){if(Pt===4||Pt===3){Pt=0,Tg();var t=Ua,e=Vn,a=$n,n=vh;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Pt=5:(Pt=0,Vn=Ua=null,Ch(t,t.pendingLanes));var u=t.pendingLanes;if(u===0&&(za=null),ps(a),e=e.stateNode,ge&&typeof ge.onCommitFiberRoot=="function")try{ge.onCommitFiberRoot(il,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=N.T,u=Y.p,Y.p=2,N.T=null;try{for(var c=t.onRecoverableError,o=0;o<n.length;o++){var h=n[o];c(h.value,{componentStack:h.stack})}}finally{N.T=e,Y.p=u}}($n&3)!==0&&vi(),Je(t),u=t.pendingLanes,(a&4194090)!==0&&(u&42)!==0?t===Ic?Zl++:(Zl=0,Ic=t):Zl=0,Vl(0)}}function Ch(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,wl(e)))}function vi(t){return Mh(),Nh(),Dh(),zh()}function zh(){if(Pt!==5)return!1;var t=Ua,e=Wc;Wc=0;var a=ps($n),n=N.T,u=Y.p;try{Y.p=32>a?32:a,N.T=null,a=Fc,Fc=null;var c=Ua,o=$n;if(Pt=0,Vn=Ua=null,$n=0,(St&6)!==0)throw Error(s(331));var h=St;if(St|=4,gh(c.current),hh(c,c.current,o,a),St=h,Vl(0,!1),ge&&typeof ge.onPostCommitFiberRoot=="function")try{ge.onPostCommitFiberRoot(il,c)}catch{}return!0}finally{Y.p=u,N.T=n,Ch(t,e)}}function Uh(t,e,a){e=Ne(a,e),e=Nc(t.stateNode,e,2),t=wa(t,e,2),t!==null&&(cl(t,2),Je(t))}function Mt(t,e,a){if(t.tag===3)Uh(t,t,a);else for(;e!==null;){if(e.tag===3){Uh(e,t,a);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(za===null||!za.has(n))){t=Ne(a,t),a=jd(2),n=wa(e,a,2),n!==null&&(Ld(a,n,e,t),cl(n,2),Je(n));break}}e=e.return}}function ar(t,e,a){var n=t.pingCache;if(n===null){n=t.pingCache=new I0;var u=new Set;n.set(e,u)}else u=n.get(e),u===void 0&&(u=new Set,n.set(e,u));u.has(a)||($c=!0,u.add(a),t=n_.bind(null,t,e,a),e.then(t,t))}function n_(t,e,a){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,Nt===t&&(yt&a)===a&&(Ht===4||Ht===3&&(yt&62914560)===yt&&300>Qe()-kc?(St&2)===0&&Kn(t,0):Kc|=a,Zn===yt&&(Zn=0)),Je(t)}function Bh(t,e){e===0&&(e=Nf()),t=Mn(t,e),t!==null&&(cl(t,e),Je(t))}function l_(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),Bh(t,a)}function u_(t,e){var a=0;switch(t.tag){case 13:var n=t.stateNode,u=t.memoizedState;u!==null&&(a=u.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(s(314))}n!==null&&n.delete(e),Bh(t,a)}function i_(t,e){return ys(t,e)}var pi=null,kn=null,nr=!1,bi=!1,lr=!1,cn=0;function Je(t){t!==kn&&t.next===null&&(kn===null?pi=kn=t:kn=kn.next=t),bi=!0,nr||(nr=!0,c_())}function Vl(t,e){if(!lr&&bi){lr=!0;do for(var a=!1,n=pi;n!==null;){if(t!==0){var u=n.pendingLanes;if(u===0)var c=0;else{var o=n.suspendedLanes,h=n.pingedLanes;c=(1<<31-_e(42|t)+1)-1,c&=u&~(o&~h),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Lh(n,c))}else c=yt,c=Ou(n,n===Nt?c:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(c&3)===0||sl(n,c)||(a=!0,Lh(n,c));n=n.next}while(a);lr=!1}}function s_(){Hh()}function Hh(){bi=nr=!1;var t=0;cn!==0&&(g_()&&(t=cn),cn=0);for(var e=Qe(),a=null,n=pi;n!==null;){var u=n.next,c=qh(n,e);c===0?(n.next=null,a===null?pi=u:a.next=u,u===null&&(kn=a)):(a=n,(t!==0||(c&3)!==0)&&(bi=!0)),n=u}Vl(t)}function qh(t,e){for(var a=t.suspendedLanes,n=t.pingedLanes,u=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var o=31-_e(c),h=1<<o,y=u[o];y===-1?((h&a)===0||(h&n)!==0)&&(u[o]=zg(h,e)):y<=e&&(t.expiredLanes|=h),c&=~h}if(e=Nt,a=yt,a=Ou(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,a===0||t===e&&(At===2||At===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&gs(n),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||sl(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(n!==null&&gs(n),ps(a)){case 2:case 8:a=Rf;break;case 32:a=Eu;break;case 268435456:a=xf;break;default:a=Eu}return n=jh.bind(null,t),a=ys(a,n),t.callbackPriority=e,t.callbackNode=a,e}return n!==null&&n!==null&&gs(n),t.callbackPriority=2,t.callbackNode=null,2}function jh(t,e){if(Pt!==0&&Pt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(vi()&&t.callbackNode!==a)return null;var n=yt;return n=Ou(t,t===Nt?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:(bh(t,n,e),qh(t,Qe()),t.callbackNode!=null&&t.callbackNode===a?jh.bind(null,t):null)}function Lh(t,e){if(vi())return null;bh(t,e,!0)}function c_(){v_(function(){(St&6)!==0?ys(Of,s_):Hh()})}function ur(){return cn===0&&(cn=Mf()),cn}function Yh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Du(""+t)}function Xh(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function r_(t,e,a,n,u){if(e==="submit"&&a&&a.stateNode===u){var c=Yh((u[re]||null).action),o=n.submitter;o&&(e=(e=o[re]||null)?Yh(e.formAction):o.getAttribute("formAction"),e!==null&&(c=e,o=null));var h=new Bu("action","action",null,n,u);t.push({event:h,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(cn!==0){var y=o?Xh(u,o):new FormData(u);Tc(a,{pending:!0,data:y,method:u.method,action:c},null,y)}}else typeof c=="function"&&(h.preventDefault(),y=o?Xh(u,o):new FormData(u),Tc(a,{pending:!0,data:y,method:u.method,action:c},c,y))},currentTarget:u}]})}}for(var ir=0;ir<Zs.length;ir++){var sr=Zs[ir],f_=sr.toLowerCase(),o_=sr[0].toUpperCase()+sr.slice(1);Le(f_,"on"+o_)}Le(bo,"onAnimationEnd"),Le(So,"onAnimationIteration"),Le(Ao,"onAnimationStart"),Le("dblclick","onDoubleClick"),Le("focusin","onFocus"),Le("focusout","onBlur"),Le(x0,"onTransitionRun"),Le(M0,"onTransitionStart"),Le(N0,"onTransitionCancel"),Le(Eo,"onTransitionEnd"),pn("onMouseEnter",["mouseout","mouseover"]),pn("onMouseLeave",["mouseout","mouseover"]),pn("onPointerEnter",["pointerout","pointerover"]),pn("onPointerLeave",["pointerout","pointerover"]),Va("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Va("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Va("onBeforeInput",["compositionend","keypress","textInput","paste"]),Va("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Va("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Va("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $l="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),d_=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat($l));function Gh(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var n=t[a],u=n.event;n=n.listeners;t:{var c=void 0;if(e)for(var o=n.length-1;0<=o;o--){var h=n[o],y=h.instance,E=h.currentTarget;if(h=h.listener,y!==c&&u.isPropagationStopped())break t;c=h,u.currentTarget=E;try{c(u)}catch(x){si(x)}u.currentTarget=null,c=y}else for(o=0;o<n.length;o++){if(h=n[o],y=h.instance,E=h.currentTarget,h=h.listener,y!==c&&u.isPropagationStopped())break t;c=h,u.currentTarget=E;try{c(u)}catch(x){si(x)}u.currentTarget=null,c=y}}}}function ht(t,e){var a=e[bs];a===void 0&&(a=e[bs]=new Set);var n=t+"__bubble";a.has(n)||(Qh(e,t,2,!1),a.add(n))}function cr(t,e,a){var n=0;e&&(n|=4),Qh(a,t,n,e)}var Si="_reactListening"+Math.random().toString(36).slice(2);function rr(t){if(!t[Si]){t[Si]=!0,Bf.forEach(function(a){a!=="selectionchange"&&(d_.has(a)||cr(a,!1,t),cr(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Si]||(e[Si]=!0,cr("selectionchange",!1,e))}}function Qh(t,e,a,n){switch(hm(e)){case 2:var u=L_;break;case 8:u=Y_;break;default:u=Er}a=u.bind(null,e,a,t),u=void 0,!Ds||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(u=!0),n?u!==void 0?t.addEventListener(e,a,{capture:!0,passive:u}):t.addEventListener(e,a,!0):u!==void 0?t.addEventListener(e,a,{passive:u}):t.addEventListener(e,a,!1)}function fr(t,e,a,n,u){var c=n;if((e&1)===0&&(e&2)===0&&n!==null)t:for(;;){if(n===null)return;var o=n.tag;if(o===3||o===4){var h=n.stateNode.containerInfo;if(h===u)break;if(o===4)for(o=n.return;o!==null;){var y=o.tag;if((y===3||y===4)&&o.stateNode.containerInfo===u)return;o=o.return}for(;h!==null;){if(o=gn(h),o===null)return;if(y=o.tag,y===5||y===6||y===26||y===27){n=c=o;continue t}h=h.parentNode}}n=n.return}kf(function(){var E=c,x=Ms(a),z=[];t:{var w=wo.get(t);if(w!==void 0){var T=Bu,at=t;switch(t){case"keypress":if(zu(a)===0)break t;case"keydown":case"keyup":T=i0;break;case"focusin":at="focus",T=Bs;break;case"focusout":at="blur",T=Bs;break;case"beforeblur":case"afterblur":T=Bs;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=If;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=Jg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=r0;break;case bo:case So:case Ao:T=Fg;break;case Eo:T=o0;break;case"scroll":case"scrollend":T=$g;break;case"wheel":T=h0;break;case"copy":case"cut":case"paste":T=Pg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=to;break;case"toggle":case"beforetoggle":T=y0}var I=(e&4)!==0,Ot=!I&&(t==="scroll"||t==="scrollend"),p=I?w!==null?w+"Capture":null:w;I=[];for(var _=E,S;_!==null;){var D=_;if(S=D.stateNode,D=D.tag,D!==5&&D!==26&&D!==27||S===null||p===null||(D=ol(_,p),D!=null&&I.push(Kl(_,D,S))),Ot)break;_=_.return}0<I.length&&(w=new T(w,at,null,a,x),z.push({event:w,listeners:I}))}}if((e&7)===0){t:{if(w=t==="mouseover"||t==="pointerover",T=t==="mouseout"||t==="pointerout",w&&a!==xs&&(at=a.relatedTarget||a.fromElement)&&(gn(at)||at[yn]))break t;if((T||w)&&(w=x.window===x?x:(w=x.ownerDocument)?w.defaultView||w.parentWindow:window,T?(at=a.relatedTarget||a.toElement,T=E,at=at?gn(at):null,at!==null&&(Ot=d(at),I=at.tag,at!==Ot||I!==5&&I!==27&&I!==6)&&(at=null)):(T=null,at=E),T!==at)){if(I=If,D="onMouseLeave",p="onMouseEnter",_="mouse",(t==="pointerout"||t==="pointerover")&&(I=to,D="onPointerLeave",p="onPointerEnter",_="pointer"),Ot=T==null?w:fl(T),S=at==null?w:fl(at),w=new I(D,_+"leave",T,a,x),w.target=Ot,w.relatedTarget=S,D=null,gn(x)===E&&(I=new I(p,_+"enter",at,a,x),I.target=S,I.relatedTarget=Ot,D=I),Ot=D,T&&at)e:{for(I=T,p=at,_=0,S=I;S;S=Wn(S))_++;for(S=0,D=p;D;D=Wn(D))S++;for(;0<_-S;)I=Wn(I),_--;for(;0<S-_;)p=Wn(p),S--;for(;_--;){if(I===p||p!==null&&I===p.alternate)break e;I=Wn(I),p=Wn(p)}I=null}else I=null;T!==null&&Zh(z,w,T,I,!1),at!==null&&Ot!==null&&Zh(z,Ot,at,I,!0)}}t:{if(w=E?fl(E):window,T=w.nodeName&&w.nodeName.toLowerCase(),T==="select"||T==="input"&&w.type==="file")var G=co;else if(io(w))if(ro)G=T0;else{G=E0;var ft=A0}else T=w.nodeName,!T||T.toLowerCase()!=="input"||w.type!=="checkbox"&&w.type!=="radio"?E&&Rs(E.elementType)&&(G=co):G=w0;if(G&&(G=G(t,E))){so(z,G,a,x);break t}ft&&ft(t,w,E),t==="focusout"&&E&&w.type==="number"&&E.memoizedProps.value!=null&&Os(w,"number",w.value)}switch(ft=E?fl(E):window,t){case"focusin":(io(ft)||ft.contentEditable==="true")&&(On=ft,Xs=E,pl=null);break;case"focusout":pl=Xs=On=null;break;case"mousedown":Gs=!0;break;case"contextmenu":case"mouseup":case"dragend":Gs=!1,vo(z,a,x);break;case"selectionchange":if(R0)break;case"keydown":case"keyup":vo(z,a,x)}var K;if(qs)t:{switch(t){case"compositionstart":var P="onCompositionStart";break t;case"compositionend":P="onCompositionEnd";break t;case"compositionupdate":P="onCompositionUpdate";break t}P=void 0}else Tn?lo(t,a)&&(P="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(P="onCompositionStart");P&&(eo&&a.locale!=="ko"&&(Tn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Tn&&(K=Wf()):(ba=x,Cs="value"in ba?ba.value:ba.textContent,Tn=!0)),ft=Ai(E,P),0<ft.length&&(P=new Pf(P,t,null,a,x),z.push({event:P,listeners:ft}),K?P.data=K:(K=uo(a),K!==null&&(P.data=K)))),(K=_0?v0(t,a):p0(t,a))&&(P=Ai(E,"onBeforeInput"),0<P.length&&(ft=new Pf("onBeforeInput","beforeinput",null,a,x),z.push({event:ft,listeners:P}),ft.data=K)),r_(z,t,E,a,x)}Gh(z,e)})}function Kl(t,e,a){return{instance:t,listener:e,currentTarget:a}}function Ai(t,e){for(var a=e+"Capture",n=[];t!==null;){var u=t,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=ol(t,a),u!=null&&n.unshift(Kl(t,u,c)),u=ol(t,e),u!=null&&n.push(Kl(t,u,c))),t.tag===3)return n;t=t.return}return[]}function Wn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Zh(t,e,a,n,u){for(var c=e._reactName,o=[];a!==null&&a!==n;){var h=a,y=h.alternate,E=h.stateNode;if(h=h.tag,y!==null&&y===n)break;h!==5&&h!==26&&h!==27||E===null||(y=E,u?(E=ol(a,c),E!=null&&o.unshift(Kl(a,E,y))):u||(E=ol(a,c),E!=null&&o.push(Kl(a,E,y)))),a=a.return}o.length!==0&&t.push({event:e,listeners:o})}var h_=/\r\n?/g,m_=/\u0000|\uFFFD/g;function Vh(t){return(typeof t=="string"?t:""+t).replace(h_,`
`).replace(m_,"")}function $h(t,e){return e=Vh(e),Vh(t)===e}function Ei(){}function Tt(t,e,a,n,u,c){switch(a){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||An(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&An(t,""+n);break;case"className":xu(t,"class",n);break;case"tabIndex":xu(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":xu(t,a,n);break;case"style":Kf(t,n,c);break;case"data":if(e!=="object"){xu(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(a);break}n=Du(""+n),t.setAttribute(a,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(e!=="input"&&Tt(t,e,"name",u.name,u,null),Tt(t,e,"formEncType",u.formEncType,u,null),Tt(t,e,"formMethod",u.formMethod,u,null),Tt(t,e,"formTarget",u.formTarget,u,null)):(Tt(t,e,"encType",u.encType,u,null),Tt(t,e,"method",u.method,u,null),Tt(t,e,"target",u.target,u,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(a);break}n=Du(""+n),t.setAttribute(a,n);break;case"onClick":n!=null&&(t.onclick=Ei);break;case"onScroll":n!=null&&ht("scroll",t);break;case"onScrollEnd":n!=null&&ht("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(a=n.__html,a!=null){if(u.children!=null)throw Error(s(60));t.innerHTML=a}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}a=Du(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(a,""+n):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":n===!0?t.setAttribute(a,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(a,n):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(a,n):t.removeAttribute(a);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(a):t.setAttribute(a,n);break;case"popover":ht("beforetoggle",t),ht("toggle",t),Ru(t,"popover",n);break;case"xlinkActuate":Pe(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":Pe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":Pe(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":Pe(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":Pe(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":Pe(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":Ru(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Zg.get(a)||a,Ru(t,a,n))}}function or(t,e,a,n,u,c){switch(a){case"style":Kf(t,n,c);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(a=n.__html,a!=null){if(u.children!=null)throw Error(s(60));t.innerHTML=a}}break;case"children":typeof n=="string"?An(t,n):(typeof n=="number"||typeof n=="bigint")&&An(t,""+n);break;case"onScroll":n!=null&&ht("scroll",t);break;case"onScrollEnd":n!=null&&ht("scrollend",t);break;case"onClick":n!=null&&(t.onclick=Ei);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Hf.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(u=a.endsWith("Capture"),e=a.slice(2,u?a.length-7:void 0),c=t[re]||null,c=c!=null?c[a]:null,typeof c=="function"&&t.removeEventListener(e,c,u),typeof n=="function")){typeof c!="function"&&c!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,n,u);break t}a in t?t[a]=n:n===!0?t.setAttribute(a,""):Ru(t,a,n)}}}function te(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ht("error",t),ht("load",t);var n=!1,u=!1,c;for(c in a)if(a.hasOwnProperty(c)){var o=a[c];if(o!=null)switch(c){case"src":n=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Tt(t,e,c,o,a,null)}}u&&Tt(t,e,"srcSet",a.srcSet,a,null),n&&Tt(t,e,"src",a.src,a,null);return;case"input":ht("invalid",t);var h=c=o=u=null,y=null,E=null;for(n in a)if(a.hasOwnProperty(n)){var x=a[n];if(x!=null)switch(n){case"name":u=x;break;case"type":o=x;break;case"checked":y=x;break;case"defaultChecked":E=x;break;case"value":c=x;break;case"defaultValue":h=x;break;case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(s(137,e));break;default:Tt(t,e,n,x,a,null)}}Qf(t,c,h,y,E,o,u,!1),Mu(t);return;case"select":ht("invalid",t),n=o=c=null;for(u in a)if(a.hasOwnProperty(u)&&(h=a[u],h!=null))switch(u){case"value":c=h;break;case"defaultValue":o=h;break;case"multiple":n=h;default:Tt(t,e,u,h,a,null)}e=c,a=o,t.multiple=!!n,e!=null?Sn(t,!!n,e,!1):a!=null&&Sn(t,!!n,a,!0);return;case"textarea":ht("invalid",t),c=u=n=null;for(o in a)if(a.hasOwnProperty(o)&&(h=a[o],h!=null))switch(o){case"value":n=h;break;case"defaultValue":u=h;break;case"children":c=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(s(91));break;default:Tt(t,e,o,h,a,null)}Vf(t,n,u,c),Mu(t);return;case"option":for(y in a)if(a.hasOwnProperty(y)&&(n=a[y],n!=null))switch(y){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:Tt(t,e,y,n,a,null)}return;case"dialog":ht("beforetoggle",t),ht("toggle",t),ht("cancel",t),ht("close",t);break;case"iframe":case"object":ht("load",t);break;case"video":case"audio":for(n=0;n<$l.length;n++)ht($l[n],t);break;case"image":ht("error",t),ht("load",t);break;case"details":ht("toggle",t);break;case"embed":case"source":case"link":ht("error",t),ht("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in a)if(a.hasOwnProperty(E)&&(n=a[E],n!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Tt(t,e,E,n,a,null)}return;default:if(Rs(e)){for(x in a)a.hasOwnProperty(x)&&(n=a[x],n!==void 0&&or(t,e,x,n,a,void 0));return}}for(h in a)a.hasOwnProperty(h)&&(n=a[h],n!=null&&Tt(t,e,h,n,a,null))}function y_(t,e,a,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,o=null,h=null,y=null,E=null,x=null;for(T in a){var z=a[T];if(a.hasOwnProperty(T)&&z!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":y=z;default:n.hasOwnProperty(T)||Tt(t,e,T,null,n,z)}}for(var w in n){var T=n[w];if(z=a[w],n.hasOwnProperty(w)&&(T!=null||z!=null))switch(w){case"type":c=T;break;case"name":u=T;break;case"checked":E=T;break;case"defaultChecked":x=T;break;case"value":o=T;break;case"defaultValue":h=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(s(137,e));break;default:T!==z&&Tt(t,e,w,T,n,z)}}Ts(t,o,h,y,E,x,c,u);return;case"select":T=o=h=w=null;for(c in a)if(y=a[c],a.hasOwnProperty(c)&&y!=null)switch(c){case"value":break;case"multiple":T=y;default:n.hasOwnProperty(c)||Tt(t,e,c,null,n,y)}for(u in n)if(c=n[u],y=a[u],n.hasOwnProperty(u)&&(c!=null||y!=null))switch(u){case"value":w=c;break;case"defaultValue":h=c;break;case"multiple":o=c;default:c!==y&&Tt(t,e,u,c,n,y)}e=h,a=o,n=T,w!=null?Sn(t,!!a,w,!1):!!n!=!!a&&(e!=null?Sn(t,!!a,e,!0):Sn(t,!!a,a?[]:"",!1));return;case"textarea":T=w=null;for(h in a)if(u=a[h],a.hasOwnProperty(h)&&u!=null&&!n.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Tt(t,e,h,null,n,u)}for(o in n)if(u=n[o],c=a[o],n.hasOwnProperty(o)&&(u!=null||c!=null))switch(o){case"value":w=u;break;case"defaultValue":T=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==c&&Tt(t,e,o,u,n,c)}Zf(t,w,T);return;case"option":for(var at in a)if(w=a[at],a.hasOwnProperty(at)&&w!=null&&!n.hasOwnProperty(at))switch(at){case"selected":t.selected=!1;break;default:Tt(t,e,at,null,n,w)}for(y in n)if(w=n[y],T=a[y],n.hasOwnProperty(y)&&w!==T&&(w!=null||T!=null))switch(y){case"selected":t.selected=w&&typeof w!="function"&&typeof w!="symbol";break;default:Tt(t,e,y,w,n,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in a)w=a[I],a.hasOwnProperty(I)&&w!=null&&!n.hasOwnProperty(I)&&Tt(t,e,I,null,n,w);for(E in n)if(w=n[E],T=a[E],n.hasOwnProperty(E)&&w!==T&&(w!=null||T!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(s(137,e));break;default:Tt(t,e,E,w,n,T)}return;default:if(Rs(e)){for(var Ot in a)w=a[Ot],a.hasOwnProperty(Ot)&&w!==void 0&&!n.hasOwnProperty(Ot)&&or(t,e,Ot,void 0,n,w);for(x in n)w=n[x],T=a[x],!n.hasOwnProperty(x)||w===T||w===void 0&&T===void 0||or(t,e,x,w,n,T);return}}for(var p in a)w=a[p],a.hasOwnProperty(p)&&w!=null&&!n.hasOwnProperty(p)&&Tt(t,e,p,null,n,w);for(z in n)w=n[z],T=a[z],!n.hasOwnProperty(z)||w===T||w==null&&T==null||Tt(t,e,z,w,n,T)}var dr=null,hr=null;function wi(t){return t.nodeType===9?t:t.ownerDocument}function Kh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Jh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function mr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var yr=null;function g_(){var t=window.event;return t&&t.type==="popstate"?t===yr?!1:(yr=t,!0):(yr=null,!1)}var kh=typeof setTimeout=="function"?setTimeout:void 0,__=typeof clearTimeout=="function"?clearTimeout:void 0,Wh=typeof Promise=="function"?Promise:void 0,v_=typeof queueMicrotask=="function"?queueMicrotask:typeof Wh<"u"?function(t){return Wh.resolve(null).then(t).catch(p_)}:kh;function p_(t){setTimeout(function(){throw t})}function Ha(t){return t==="head"}function Fh(t,e){var a=e,n=0,u=0;do{var c=a.nextSibling;if(t.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<n&&8>n){a=n;var o=t.ownerDocument;if(a&1&&Jl(o.documentElement),a&2&&Jl(o.body),a&4)for(a=o.head,Jl(a),o=a.firstChild;o;){var h=o.nextSibling,y=o.nodeName;o[rl]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&o.rel.toLowerCase()==="stylesheet"||a.removeChild(o),o=h}}if(u===0){t.removeChild(c),au(e);return}u--}else a==="$"||a==="$?"||a==="$!"?u++:n=a.charCodeAt(0)-48;else n=0;a=c}while(a);au(e)}function gr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":gr(a),Ss(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function b_(t,e,a,n){for(;t.nodeType===1;){var u=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[rl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==u.rel||t.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||t.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||t.getAttribute("title")!==(u.title==null?null:u.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(u.src==null?null:u.src)||t.getAttribute("type")!==(u.type==null?null:u.type)||t.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=Xe(t.nextSibling),t===null)break}return null}function S_(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=Xe(t.nextSibling),t===null))return null;return t}function _r(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function A_(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var n=function(){e(),a.removeEventListener("DOMContentLoaded",n)};a.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function Xe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var vr=null;function Ih(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function Ph(t,e,a){switch(e=wi(a),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function Jl(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ss(t)}var He=new Map,tm=new Set;function Ti(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var ha=Y.d;Y.d={f:E_,r:w_,D:T_,C:O_,L:R_,m:x_,X:N_,S:M_,M:D_};function E_(){var t=ha.f(),e=gi();return t||e}function w_(t){var e=_n(t);e!==null&&e.tag===5&&e.type==="form"?bd(e):ha.r(t)}var Fn=typeof document>"u"?null:document;function em(t,e,a){var n=Fn;if(n&&typeof e=="string"&&e){var u=Me(e);u='link[rel="'+t+'"][href="'+u+'"]',typeof a=="string"&&(u+='[crossorigin="'+a+'"]'),tm.has(u)||(tm.add(u),t={rel:t,crossOrigin:a,href:e},n.querySelector(u)===null&&(e=n.createElement("link"),te(e,"link",t),Kt(e),n.head.appendChild(e)))}}function T_(t){ha.D(t),em("dns-prefetch",t,null)}function O_(t,e){ha.C(t,e),em("preconnect",t,e)}function R_(t,e,a){ha.L(t,e,a);var n=Fn;if(n&&t&&e){var u='link[rel="preload"][as="'+Me(e)+'"]';e==="image"&&a&&a.imageSrcSet?(u+='[imagesrcset="'+Me(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(u+='[imagesizes="'+Me(a.imageSizes)+'"]')):u+='[href="'+Me(t)+'"]';var c=u;switch(e){case"style":c=In(t);break;case"script":c=Pn(t)}He.has(c)||(t=R({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),He.set(c,t),n.querySelector(u)!==null||e==="style"&&n.querySelector(kl(c))||e==="script"&&n.querySelector(Wl(c))||(e=n.createElement("link"),te(e,"link",t),Kt(e),n.head.appendChild(e)))}}function x_(t,e){ha.m(t,e);var a=Fn;if(a&&t){var n=e&&typeof e.as=="string"?e.as:"script",u='link[rel="modulepreload"][as="'+Me(n)+'"][href="'+Me(t)+'"]',c=u;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Pn(t)}if(!He.has(c)&&(t=R({rel:"modulepreload",href:t},e),He.set(c,t),a.querySelector(u)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Wl(c)))return}n=a.createElement("link"),te(n,"link",t),Kt(n),a.head.appendChild(n)}}}function M_(t,e,a){ha.S(t,e,a);var n=Fn;if(n&&t){var u=vn(n).hoistableStyles,c=In(t);e=e||"default";var o=u.get(c);if(!o){var h={loading:0,preload:null};if(o=n.querySelector(kl(c)))h.loading=5;else{t=R({rel:"stylesheet",href:t,"data-precedence":e},a),(a=He.get(c))&&pr(t,a);var y=o=n.createElement("link");Kt(y),te(y,"link",t),y._p=new Promise(function(E,x){y.onload=E,y.onerror=x}),y.addEventListener("load",function(){h.loading|=1}),y.addEventListener("error",function(){h.loading|=2}),h.loading|=4,Oi(o,e,n)}o={type:"stylesheet",instance:o,count:1,state:h},u.set(c,o)}}}function N_(t,e){ha.X(t,e);var a=Fn;if(a&&t){var n=vn(a).hoistableScripts,u=Pn(t),c=n.get(u);c||(c=a.querySelector(Wl(u)),c||(t=R({src:t,async:!0},e),(e=He.get(u))&&br(t,e),c=a.createElement("script"),Kt(c),te(c,"link",t),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},n.set(u,c))}}function D_(t,e){ha.M(t,e);var a=Fn;if(a&&t){var n=vn(a).hoistableScripts,u=Pn(t),c=n.get(u);c||(c=a.querySelector(Wl(u)),c||(t=R({src:t,async:!0,type:"module"},e),(e=He.get(u))&&br(t,e),c=a.createElement("script"),Kt(c),te(c,"link",t),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},n.set(u,c))}}function am(t,e,a,n){var u=(u=it.current)?Ti(u):null;if(!u)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=In(a.href),a=vn(u).hoistableStyles,n=a.get(e),n||(n={type:"style",instance:null,count:0,state:null},a.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=In(a.href);var c=vn(u).hoistableStyles,o=c.get(t);if(o||(u=u.ownerDocument||u,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,o),(c=u.querySelector(kl(t)))&&!c._p&&(o.instance=c,o.state.loading=5),He.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},He.set(t,a),c||C_(u,t,a,o.state))),e&&n===null)throw Error(s(528,""));return o}if(e&&n!==null)throw Error(s(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Pn(a),a=vn(u).hoistableScripts,n=a.get(e),n||(n={type:"script",instance:null,count:0,state:null},a.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function In(t){return'href="'+Me(t)+'"'}function kl(t){return'link[rel="stylesheet"]['+t+"]"}function nm(t){return R({},t,{"data-precedence":t.precedence,precedence:null})}function C_(t,e,a,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),te(e,"link",a),Kt(e),t.head.appendChild(e))}function Pn(t){return'[src="'+Me(t)+'"]'}function Wl(t){return"script[async]"+t}function lm(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+Me(a.href)+'"]');if(n)return e.instance=n,Kt(n),n;var u=R({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),Kt(n),te(n,"style",u),Oi(n,a.precedence,t),e.instance=n;case"stylesheet":u=In(a.href);var c=t.querySelector(kl(u));if(c)return e.state.loading|=4,e.instance=c,Kt(c),c;n=nm(a),(u=He.get(u))&&pr(n,u),c=(t.ownerDocument||t).createElement("link"),Kt(c);var o=c;return o._p=new Promise(function(h,y){o.onload=h,o.onerror=y}),te(c,"link",n),e.state.loading|=4,Oi(c,a.precedence,t),e.instance=c;case"script":return c=Pn(a.src),(u=t.querySelector(Wl(c)))?(e.instance=u,Kt(u),u):(n=a,(u=He.get(c))&&(n=R({},a),br(n,u)),t=t.ownerDocument||t,u=t.createElement("script"),Kt(u),te(u,"link",n),t.head.appendChild(u),e.instance=u);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(n=e.instance,e.state.loading|=4,Oi(n,a.precedence,t));return e.instance}function Oi(t,e,a){for(var n=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=n.length?n[n.length-1]:null,c=u,o=0;o<n.length;o++){var h=n[o];if(h.dataset.precedence===e)c=h;else if(c!==u)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function pr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function br(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ri=null;function um(t,e,a){if(Ri===null){var n=new Map,u=Ri=new Map;u.set(a,n)}else u=Ri,n=u.get(a),n||(n=new Map,u.set(a,n));if(n.has(t))return n;for(n.set(t,null),a=a.getElementsByTagName(t),u=0;u<a.length;u++){var c=a[u];if(!(c[rl]||c[ne]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var o=c.getAttribute(e)||"";o=t+o;var h=n.get(o);h?h.push(c):n.set(o,[c])}}return n}function im(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function z_(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function sm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Fl=null;function U_(){}function B_(t,e,a){if(Fl===null)throw Error(s(475));var n=Fl;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var u=In(a.href),c=t.querySelector(kl(u));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(n.count++,n=xi.bind(n),t.then(n,n)),e.state.loading|=4,e.instance=c,Kt(c);return}c=t.ownerDocument||t,a=nm(a),(u=He.get(u))&&pr(a,u),c=c.createElement("link"),Kt(c);var o=c;o._p=new Promise(function(h,y){o.onload=h,o.onerror=y}),te(c,"link",a),e.instance=c}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(n.count++,e=xi.bind(n),t.addEventListener("load",e),t.addEventListener("error",e))}}function H_(){if(Fl===null)throw Error(s(475));var t=Fl;return t.stylesheets&&t.count===0&&Sr(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&Sr(t,t.stylesheets),t.unsuspend){var n=t.unsuspend;t.unsuspend=null,n()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function xi(){if(this.count--,this.count===0){if(this.stylesheets)Sr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Mi=null;function Sr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Mi=new Map,e.forEach(q_,t),Mi=null,xi.call(t))}function q_(t,e){if(!(e.state.loading&4)){var a=Mi.get(t);if(a)var n=a.get(null);else{a=new Map,Mi.set(t,a);for(var u=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var o=u[c];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(a.set(o.dataset.precedence,o),n=o)}n&&a.set(null,n)}u=e.instance,o=u.getAttribute("data-precedence"),c=a.get(o)||n,c===n&&a.set(null,u),a.set(o,u),this.count++,n=xi.bind(this),u.addEventListener("load",n),u.addEventListener("error",n),c?c.parentNode.insertBefore(u,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(u,t.firstChild)),e.state.loading|=4}}var Il={$$typeof:J,Provider:null,Consumer:null,_currentValue:et,_currentValue2:et,_threadCount:0};function j_(t,e,a,n,u,c,o,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=_s(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_s(0),this.hiddenUpdates=_s(null),this.identifierPrefix=n,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function cm(t,e,a,n,u,c,o,h,y,E,x,z){return t=new j_(t,e,a,o,h,y,E,z),e=1,c===!0&&(e|=24),c=pe(3,null,null,e),t.current=c,c.stateNode=t,e=ac(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:n,isDehydrated:a,cache:e},ic(c),t}function rm(t){return t?(t=Nn,t):Nn}function fm(t,e,a,n,u,c){u=rm(u),n.context===null?n.context=u:n.pendingContext=u,n=Ea(e),n.payload={element:a},c=c===void 0?null:c,c!==null&&(n.callback=c),a=wa(t,n,e),a!==null&&(we(a,t,e),xl(a,t,e))}function om(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function Ar(t,e){om(t,e),(t=t.alternate)&&om(t,e)}function dm(t){if(t.tag===13){var e=Mn(t,67108864);e!==null&&we(e,t,67108864),Ar(t,67108864)}}var Ni=!0;function L_(t,e,a,n){var u=N.T;N.T=null;var c=Y.p;try{Y.p=2,Er(t,e,a,n)}finally{Y.p=c,N.T=u}}function Y_(t,e,a,n){var u=N.T;N.T=null;var c=Y.p;try{Y.p=8,Er(t,e,a,n)}finally{Y.p=c,N.T=u}}function Er(t,e,a,n){if(Ni){var u=wr(n);if(u===null)fr(t,e,n,Di,a),mm(t,n);else if(G_(u,t,e,a,n))n.stopPropagation();else if(mm(t,n),e&4&&-1<X_.indexOf(t)){for(;u!==null;){var c=_n(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var o=Za(c.pendingLanes);if(o!==0){var h=c;for(h.pendingLanes|=2,h.entangledLanes|=2;o;){var y=1<<31-_e(o);h.entanglements[1]|=y,o&=~y}Je(c),(St&6)===0&&(mi=Qe()+500,Vl(0))}}break;case 13:h=Mn(c,2),h!==null&&we(h,c,2),gi(),Ar(c,2)}if(c=wr(n),c===null&&fr(t,e,n,Di,a),c===u)break;u=c}u!==null&&n.stopPropagation()}else fr(t,e,n,null,a)}}function wr(t){return t=Ms(t),Tr(t)}var Di=null;function Tr(t){if(Di=null,t=gn(t),t!==null){var e=d(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=m(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Di=t,null}function hm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Og()){case Of:return 2;case Rf:return 8;case Eu:case Rg:return 32;case xf:return 268435456;default:return 32}default:return 32}}var Or=!1,qa=null,ja=null,La=null,Pl=new Map,tu=new Map,Ya=[],X_="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function mm(t,e){switch(t){case"focusin":case"focusout":qa=null;break;case"dragenter":case"dragleave":ja=null;break;case"mouseover":case"mouseout":La=null;break;case"pointerover":case"pointerout":Pl.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":tu.delete(e.pointerId)}}function eu(t,e,a,n,u,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:a,eventSystemFlags:n,nativeEvent:c,targetContainers:[u]},e!==null&&(e=_n(e),e!==null&&dm(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,u!==null&&e.indexOf(u)===-1&&e.push(u),t)}function G_(t,e,a,n,u){switch(e){case"focusin":return qa=eu(qa,t,e,a,n,u),!0;case"dragenter":return ja=eu(ja,t,e,a,n,u),!0;case"mouseover":return La=eu(La,t,e,a,n,u),!0;case"pointerover":var c=u.pointerId;return Pl.set(c,eu(Pl.get(c)||null,t,e,a,n,u)),!0;case"gotpointercapture":return c=u.pointerId,tu.set(c,eu(tu.get(c)||null,t,e,a,n,u)),!0}return!1}function ym(t){var e=gn(t.target);if(e!==null){var a=d(e);if(a!==null){if(e=a.tag,e===13){if(e=m(a),e!==null){t.blockedOn=e,Bg(t.priority,function(){if(a.tag===13){var n=Ee();n=vs(n);var u=Mn(a,n);u!==null&&we(u,a,n),Ar(a,n)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ci(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=wr(t.nativeEvent);if(a===null){a=t.nativeEvent;var n=new a.constructor(a.type,a);xs=n,a.target.dispatchEvent(n),xs=null}else return e=_n(a),e!==null&&dm(e),t.blockedOn=a,!1;e.shift()}return!0}function gm(t,e,a){Ci(t)&&a.delete(e)}function Q_(){Or=!1,qa!==null&&Ci(qa)&&(qa=null),ja!==null&&Ci(ja)&&(ja=null),La!==null&&Ci(La)&&(La=null),Pl.forEach(gm),tu.forEach(gm)}function zi(t,e){t.blockedOn===e&&(t.blockedOn=null,Or||(Or=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Q_)))}var Ui=null;function _m(t){Ui!==t&&(Ui=t,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Ui===t&&(Ui=null);for(var e=0;e<t.length;e+=3){var a=t[e],n=t[e+1],u=t[e+2];if(typeof n!="function"){if(Tr(n||a)===null)continue;break}var c=_n(a);c!==null&&(t.splice(e,3),e-=3,Tc(c,{pending:!0,data:u,method:a.method,action:n},n,u))}}))}function au(t){function e(y){return zi(y,t)}qa!==null&&zi(qa,t),ja!==null&&zi(ja,t),La!==null&&zi(La,t),Pl.forEach(e),tu.forEach(e);for(var a=0;a<Ya.length;a++){var n=Ya[a];n.blockedOn===t&&(n.blockedOn=null)}for(;0<Ya.length&&(a=Ya[0],a.blockedOn===null);)ym(a),a.blockedOn===null&&Ya.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(n=0;n<a.length;n+=3){var u=a[n],c=a[n+1],o=u[re]||null;if(typeof c=="function")o||_m(a);else if(o){var h=null;if(c&&c.hasAttribute("formAction")){if(u=c,o=c[re]||null)h=o.formAction;else if(Tr(u)!==null)continue}else h=o.action;typeof h=="function"?a[n+1]=h:(a.splice(n,3),n-=3),_m(a)}}}function Rr(t){this._internalRoot=t}Bi.prototype.render=Rr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var a=e.current,n=Ee();fm(a,n,t,e,null,null)},Bi.prototype.unmount=Rr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;fm(t.current,2,null,t,null,null),gi(),e[yn]=null}};function Bi(t){this._internalRoot=t}Bi.prototype.unstable_scheduleHydration=function(t){if(t){var e=zf();t={blockedOn:null,target:t,priority:e};for(var a=0;a<Ya.length&&e!==0&&e<Ya[a].priority;a++);Ya.splice(a,0,t),a===0&&ym(t)}};var vm=l.version;if(vm!=="19.1.0")throw Error(s(527,vm,"19.1.0"));Y.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=A(e),t=t!==null?v(t):null,t=t===null?null:t.stateNode,t};var Z_={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Hi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Hi.isDisabled&&Hi.supportsFiber)try{il=Hi.inject(Z_),ge=Hi}catch{}}return lu.createRoot=function(t,e){if(!f(t))throw Error(s(299));var a=!1,n="",u=Ud,c=Bd,o=Hd,h=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(u=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=cm(t,1,!1,null,null,a,n,u,c,o,h,null),t[yn]=e.current,rr(t),new Rr(e)},lu.hydrateRoot=function(t,e,a){if(!f(t))throw Error(s(299));var n=!1,u="",c=Ud,o=Bd,h=Hd,y=null,E=null;return a!=null&&(a.unstable_strictMode===!0&&(n=!0),a.identifierPrefix!==void 0&&(u=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(o=a.onCaughtError),a.onRecoverableError!==void 0&&(h=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(y=a.unstable_transitionCallbacks),a.formState!==void 0&&(E=a.formState)),e=cm(t,1,!0,e,a??null,n,u,c,o,h,y,E),e.context=rm(null),a=e.current,n=Ee(),n=vs(n),u=Ea(n),u.callback=null,wa(a,u,n),a=n,e.current.lanes=a,cl(e,a),Je(e),t[yn]=e.current,rr(t),new Bi(e)},lu.version="19.1.0",lu}var xm;function tv(){if(xm)return Nr.exports;xm=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(l){console.error(l)}}return r(),Nr.exports=P_(),Nr.exports}var ev=tv();const Qr=["cat","dog","bird","fish","horse","elephant","tiger","lion","bear","wolf","rabbit","mouse","snake","frog","butterfly","spider","whale","dolphin","shark","eagle","apple","banana","orange","bread","cheese","pizza","cake","cookie","chocolate","coffee","tea","water","milk","juice","sandwich","pasta","rice","chicken","beef","fish","book","pen","paper","computer","phone","car","house","chair","table","window","door","key","clock","mirror","lamp","camera","guitar","piano","bicycle","umbrella","tree","flower","grass","mountain","river","ocean","sun","moon","star","cloud","rain","snow","wind","fire","earth","stone","sand","forest","beach","island","red","blue","green","yellow","orange","purple","pink","black","white","brown","gray","silver","gold","violet","indigo","turquoise","crimson","emerald","azure","coral","run","walk","jump","swim","fly","dance","sing","read","write","draw","paint","cook","eat","sleep","dream","laugh","cry","smile","think","learn","happy","sad","angry","excited","calm","nervous","proud","grateful","curious","brave","kind","gentle","fierce","peaceful","joyful","serene","anxious","confident","humble","wise","love","hope","peace","freedom","justice","truth","beauty","wisdom","courage","strength","friendship","family","home","journey","adventure","mystery","magic","wonder","miracle","destiny","morning","afternoon","evening","night","today","tomorrow","yesterday","future","past","present","minute","hour","day","week","month","year","season","spring","summer","autumn","winter","city","town","village","country","world","universe","space","planet","galaxy","cosmos","school","hospital","library","museum","park","garden","market","restaurant","hotel","theater","internet","website","email","software","hardware","robot","artificial","intelligence","digital","virtual","network","database","algorithm","programming","coding","innovation","technology","science","research","discovery"],av=()=>{const r=Math.floor(Math.random()*Qr.length);return Qr[r]},nv=r=>/^[a-zA-Z]+$/.test(r)&&r.length>=2,lv=({currentGuess:r,setCurrentGuess:l,onGuess:i,isLoading:s,guesses:f})=>{const[d,m]=ga.useState(""),b=v=>{if(v.preventDefault(),!r.trim()){m("Please enter a word");return}if(!nv(r)){m("Please enter a valid word (letters only, at least 2 characters)");return}if(f.some(R=>R.word===r.toLowerCase())){m("You already guessed this word!");return}m(""),i(r)},A=v=>{l(v.target.value),d&&m("")};return L.jsxs("div",{className:"guess-input-container",children:[L.jsxs("form",{onSubmit:b,className:"guess-form",children:[L.jsxs("div",{className:"input-group",children:[L.jsx("input",{type:"text",value:r,onChange:A,placeholder:"Enter your guess...",className:`guess-input ${d?"error":""}`,disabled:s,autoFocus:!0}),L.jsx("button",{type:"submit",className:"guess-submit",disabled:s||!r.trim(),children:s?"Thinking...":"Guess"})]}),d&&L.jsx("div",{className:"error-message",children:d})]}),L.jsxs("div",{className:"instructions",children:[L.jsxs("p",{children:["💡 ",L.jsx("strong",{children:"How to play:"})," Guess words related to the secret word. Higher scores mean you're closer!"]}),L.jsx("p",{children:"🎯 Score ranges from 1 (cold) to 10,000 (exact match)"})]})]})},uv=({guess:r,rank:l,isWinning:i})=>{const s=m=>m===1e4?"#6aaa64":m>=8e3?"#c9b458":m>=6e3?"#f5a623":m>=4e3?"#d73027":m>=2e3?"#9c27b0":"#787c7e",f=m=>Math.max(m/1e4*100,2),d=m=>m===1e4?"🔥 PERFECT!":m>=9e3?"🔥 Burning hot!":m>=8e3?"🌡️ Very hot":m>=7e3?"🌡️ Hot":m>=6e3?"🌡️ Warm":m>=4e3?"🌡️ Cool":m>=2e3?"❄️ Cold":"🧊 Freezing";return L.jsxs("div",{className:`guess-item ${i?"winning":""}`,children:[L.jsxs("div",{className:"guess-content",children:[L.jsxs("div",{className:"guess-info",children:[L.jsxs("span",{className:"rank",children:["#",l]}),L.jsx("span",{className:"word",children:r.word}),L.jsx("span",{className:"temperature",children:d(r.score)})]}),L.jsxs("div",{className:"score-section",children:[L.jsx("span",{className:"score",children:r.score.toLocaleString()}),L.jsx("div",{className:"score-bar-container",children:L.jsx("div",{className:"score-bar",style:{width:`${f(r.score)}%`,backgroundColor:s(r.score)}})})]})]}),i&&L.jsxs("div",{className:"winning-animation",children:[L.jsx("span",{className:"confetti",children:"🎉"}),L.jsx("span",{className:"confetti",children:"🎊"}),L.jsx("span",{className:"confetti",children:"✨"})]})]})},iv=({guesses:r})=>r.length===0?L.jsx("div",{className:"guesses-container",children:L.jsx("div",{className:"no-guesses",children:L.jsx("p",{children:"No guesses yet. Start by entering a word above!"})})}):L.jsxs("div",{className:"guesses-container",children:[L.jsxs("div",{className:"guesses-header",children:[L.jsxs("h3",{children:["Your Guesses (",r.length,")"]}),L.jsx("p",{className:"ranking-info",children:"Ranked by similarity to the secret word"})]}),L.jsx("div",{className:"guesses-list",children:r.map((l,i)=>L.jsx(uv,{guess:l,rank:i+1,isWinning:l.score===1e4},l.id))})]}),sv=({secretWord:r,attemptCount:l,guesses:i})=>{const[s,f]=ga.useState(!1),d=()=>{const A=i.slice(0,5).map((v,R)=>`${R+1}. ${v.word} (${v.score.toLocaleString()})`).join(`
`);return`🎯 Contexto - I found the word "${r}" in ${l} tries!

Top guesses:
${A}

Play at: ${window.location.href}`},m=async()=>{const A=d();if(navigator.share)try{await navigator.share({title:"Contexto Game Result",text:A})}catch{console.log("Share cancelled")}else try{await navigator.clipboard.writeText(A),f(!0),setTimeout(()=>f(!1),2e3)}catch{console.error("Failed to copy to clipboard")}},b=A=>A<=5?"🏆 Incredible! You're a word wizard!":A<=10?"🌟 Excellent! Great semantic intuition!":A<=15?"👏 Well done! Nice work!":A<=25?"👍 Good job! You got there!":"🎯 Success! Every guess teaches us something!";return L.jsx("div",{className:"game-result",children:L.jsxs("div",{className:"result-content",children:[L.jsxs("div",{className:"celebration",children:[L.jsxs("div",{className:"confetti-rain",children:[L.jsx("span",{children:"🎉"}),L.jsx("span",{children:"🎊"}),L.jsx("span",{children:"✨"}),L.jsx("span",{children:"🌟"}),L.jsx("span",{children:"🎈"})]}),L.jsx("h2",{className:"result-title",children:"Congratulations!"}),L.jsxs("div",{className:"secret-word",children:["The word was: ",L.jsx("strong",{children:r})]}),L.jsxs("div",{className:"performance",children:[L.jsxs("div",{className:"attempt-count",children:["You found it in ",L.jsx("strong",{children:l})," ",l===1?"try":"tries","!"]}),L.jsx("div",{className:"performance-message",children:b(l)})]})]}),L.jsxs("div",{className:"share-section",children:[L.jsx("button",{className:`share-btn ${s?"copied":""}`,onClick:m,children:s?"✅ Copied!":"📤 Share Result"}),L.jsxs("div",{className:"share-preview",children:[L.jsx("h4",{children:"Share preview:"}),L.jsx("pre",{className:"share-text",children:d()})]})]})]})})},cv=({guesses:r,currentGuess:l,setCurrentGuess:i,onGuess:s,isLoading:f,gameStatus:d,secretWord:m})=>L.jsx("main",{className:"game-board",children:L.jsxs("div",{className:"game-container",children:[d==="playing"&&L.jsx(lv,{currentGuess:l,setCurrentGuess:i,onGuess:s,isLoading:f,guesses:r}),d==="won"&&L.jsx(sv,{secretWord:m,attemptCount:r.length,guesses:r}),L.jsx(iv,{guesses:r})]})}),rv=({onNewGame:r,gameStatus:l,attemptCount:i})=>L.jsx("header",{className:"header",children:L.jsxs("div",{className:"header-content",children:[L.jsx("h1",{className:"title",children:"Contexto"}),L.jsx("p",{className:"subtitle",children:"Find the secret word using semantic clues!"}),L.jsxs("div",{className:"game-info",children:[l==="won"&&L.jsxs("div",{className:"status-message won",children:["🎉 Congratulations! You found it in ",i," tries!"]}),l==="playing"&&i>0&&L.jsxs("div",{className:"status-message playing",children:["Attempts: ",i]})]}),L.jsx("button",{className:"new-game-btn",onClick:r,children:"New Game"})]})});function lt(r,l,i,s,f){if(typeof l=="function"?r!==l||!0:!l.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return l.set(r,i),i}function O(r,l,i,s){if(i==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof l=="function"?r!==l||!s:!l.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return i==="m"?s:i==="a"?s.call(r):s?s.value:l.get(r)}let ey=function(){const{crypto:r}=globalThis;if(r!=null&&r.randomUUID)return ey=r.randomUUID.bind(r),r.randomUUID();const l=new Uint8Array(1),i=r?()=>r.getRandomValues(l)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,s=>(+s^i()&15>>+s/4).toString(16))};function Zr(r){return typeof r=="object"&&r!==null&&("name"in r&&r.name==="AbortError"||"message"in r&&String(r.message).includes("FetchRequestCanceledException"))}const Vr=r=>{if(r instanceof Error)return r;if(typeof r=="object"&&r!==null){try{if(Object.prototype.toString.call(r)==="[object Error]"){const l=new Error(r.message,r.cause?{cause:r.cause}:{});return r.stack&&(l.stack=r.stack),r.cause&&!l.cause&&(l.cause=r.cause),r.name&&(l.name=r.name),l}}catch{}try{return new Error(JSON.stringify(r))}catch{}}return new Error(r)};class nt extends Error{}class ae extends nt{constructor(l,i,s,f){super(`${ae.makeMessage(l,i,s)}`),this.status=l,this.headers=f,this.requestID=f==null?void 0:f.get("x-request-id"),this.error=i;const d=i;this.code=d==null?void 0:d.code,this.param=d==null?void 0:d.param,this.type=d==null?void 0:d.type}static makeMessage(l,i,s){const f=i!=null&&i.message?typeof i.message=="string"?i.message:JSON.stringify(i.message):i?JSON.stringify(i):s;return l&&f?`${l} ${f}`:l?`${l} status code (no body)`:f||"(no status code or body)"}static generate(l,i,s,f){if(!l||!f)return new is({message:s,cause:Vr(i)});const d=i==null?void 0:i.error;return l===400?new ay(l,d,s,f):l===401?new ny(l,d,s,f):l===403?new ly(l,d,s,f):l===404?new uy(l,d,s,f):l===409?new iy(l,d,s,f):l===422?new sy(l,d,s,f):l===429?new cy(l,d,s,f):l>=500?new ry(l,d,s,f):new ae(l,d,s,f)}}class qe extends ae{constructor({message:l}={}){super(void 0,void 0,l||"Request was aborted.",void 0)}}class is extends ae{constructor({message:l,cause:i}){super(void 0,void 0,l||"Connection error.",void 0),i&&(this.cause=i)}}class lf extends is{constructor({message:l}={}){super({message:l??"Request timed out."})}}class ay extends ae{}class ny extends ae{}class ly extends ae{}class uy extends ae{}class iy extends ae{}class sy extends ae{}class cy extends ae{}class ry extends ae{}class fy extends nt{constructor(){super("Could not parse response content as the length limit was reached")}}class oy extends nt{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}const fv=/^[a-z][a-z0-9+.-]*:/i,ov=r=>fv.test(r);let me=r=>(me=Array.isArray,me(r)),Mm=me;function dv(r){return typeof r!="object"?{}:r??{}}function hv(r){if(!r)return!0;for(const l in r)return!1;return!0}function mv(r,l){return Object.prototype.hasOwnProperty.call(r,l)}function Ur(r){return r!=null&&typeof r=="object"&&!Array.isArray(r)}const yv=(r,l)=>{if(typeof l!="number"||!Number.isInteger(l))throw new nt(`${r} must be an integer`);if(l<0)throw new nt(`${r} must be a positive integer`);return l},gv=r=>{try{return JSON.parse(r)}catch{return}},vu=r=>new Promise(l=>setTimeout(l,r)),al="5.5.1",_v=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function vv(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const pv=()=>{var i;const r=vv();if(r==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":al,"X-Stainless-OS":Dm(Deno.build.os),"X-Stainless-Arch":Nm(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((i=Deno.version)==null?void 0:i.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":al,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(r==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":al,"X-Stainless-OS":Dm(globalThis.process.platform??"unknown"),"X-Stainless-Arch":Nm(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};const l=bv();return l?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":al,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${l.browser}`,"X-Stainless-Runtime-Version":l.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":al,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function bv(){if(typeof navigator>"u"||!navigator)return null;const r=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:l,pattern:i}of r){const s=i.exec(navigator.userAgent);if(s){const f=s[1]||0,d=s[2]||0,m=s[3]||0;return{browser:l,version:`${f}.${d}.${m}`}}}return null}const Nm=r=>r==="x32"?"x32":r==="x86_64"||r==="x64"?"x64":r==="arm"?"arm":r==="aarch64"||r==="arm64"?"arm64":r?`other:${r}`:"unknown",Dm=r=>(r=r.toLowerCase(),r.includes("ios")?"iOS":r==="android"?"Android":r==="darwin"?"MacOS":r==="win32"?"Windows":r==="freebsd"?"FreeBSD":r==="openbsd"?"OpenBSD":r==="linux"?"Linux":r?`Other:${r}`:"Unknown");let Cm;const Sv=()=>Cm??(Cm=pv());function Av(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function dy(...r){const l=globalThis.ReadableStream;if(typeof l>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new l(...r)}function hy(r){let l=Symbol.asyncIterator in r?r[Symbol.asyncIterator]():r[Symbol.iterator]();return dy({start(){},async pull(i){const{done:s,value:f}=await l.next();s?i.close():i.enqueue(f)},async cancel(){var i;await((i=l.return)==null?void 0:i.call(l))}})}function my(r){if(r[Symbol.asyncIterator])return r;const l=r.getReader();return{async next(){try{const i=await l.read();return i!=null&&i.done&&l.releaseLock(),i}catch(i){throw l.releaseLock(),i}},async return(){const i=l.cancel();return l.releaseLock(),await i,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function Ev(r){var s,f;if(r===null||typeof r!="object")return;if(r[Symbol.asyncIterator]){await((f=(s=r[Symbol.asyncIterator]()).return)==null?void 0:f.call(s));return}const l=r.getReader(),i=l.cancel();l.releaseLock(),await i}const wv=({headers:r,body:l})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(l)}),yy="RFC3986",gy=r=>String(r),zm={RFC1738:r=>String(r).replace(/%20/g,"+"),RFC3986:gy},Tv="RFC1738";let $r=(r,l)=>($r=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty),$r(r,l));const ke=(()=>{const r=[];for(let l=0;l<256;++l)r.push("%"+((l<16?"0":"")+l.toString(16)).toUpperCase());return r})(),Br=1024,Ov=(r,l,i,s,f)=>{if(r.length===0)return r;let d=r;if(typeof r=="symbol"?d=Symbol.prototype.toString.call(r):typeof r!="string"&&(d=String(r)),i==="iso-8859-1")return escape(d).replace(/%u[0-9a-f]{4}/gi,function(b){return"%26%23"+parseInt(b.slice(2),16)+"%3B"});let m="";for(let b=0;b<d.length;b+=Br){const A=d.length>=Br?d.slice(b,b+Br):d,v=[];for(let R=0;R<A.length;++R){let M=A.charCodeAt(R);if(M===45||M===46||M===95||M===126||M>=48&&M<=57||M>=65&&M<=90||M>=97&&M<=122||f===Tv&&(M===40||M===41)){v[v.length]=A.charAt(R);continue}if(M<128){v[v.length]=ke[M];continue}if(M<2048){v[v.length]=ke[192|M>>6]+ke[128|M&63];continue}if(M<55296||M>=57344){v[v.length]=ke[224|M>>12]+ke[128|M>>6&63]+ke[128|M&63];continue}R+=1,M=65536+((M&1023)<<10|A.charCodeAt(R)&1023),v[v.length]=ke[240|M>>18]+ke[128|M>>12&63]+ke[128|M>>6&63]+ke[128|M&63]}m+=v.join("")}return m};function Rv(r){return!r||typeof r!="object"?!1:!!(r.constructor&&r.constructor.isBuffer&&r.constructor.isBuffer(r))}function Um(r,l){if(me(r)){const i=[];for(let s=0;s<r.length;s+=1)i.push(l(r[s]));return i}return l(r)}const _y={brackets(r){return String(r)+"[]"},comma:"comma",indices(r,l){return String(r)+"["+l+"]"},repeat(r){return String(r)}},vy=function(r,l){Array.prototype.push.apply(r,me(l)?l:[l])};let Bm;const $t={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Ov,encodeValuesOnly:!1,format:yy,formatter:gy,indices:!1,serializeDate(r){return(Bm??(Bm=Function.prototype.call.bind(Date.prototype.toISOString)))(r)},skipNulls:!1,strictNullHandling:!1};function xv(r){return typeof r=="string"||typeof r=="number"||typeof r=="boolean"||typeof r=="symbol"||typeof r=="bigint"}const Hr={};function py(r,l,i,s,f,d,m,b,A,v,R,M,U,C,q,Q,Z,Et){let $=r,J=Et,V=0,k=!1;for(;(J=J.get(Hr))!==void 0&&!k;){const bt=J.get(r);if(V+=1,typeof bt<"u"){if(bt===V)throw new RangeError("Cyclic object value");k=!0}typeof J.get(Hr)>"u"&&(V=0)}if(typeof v=="function"?$=v(l,$):$ instanceof Date?$=U==null?void 0:U($):i==="comma"&&me($)&&($=Um($,function(bt){return bt instanceof Date?U==null?void 0:U(bt):bt})),$===null){if(d)return A&&!Q?A(l,$t.encoder,Z,"key",C):l;$=""}if(xv($)||Rv($)){if(A){const bt=Q?l:A(l,$t.encoder,Z,"key",C);return[(q==null?void 0:q(bt))+"="+(q==null?void 0:q(A($,$t.encoder,Z,"value",C)))]}return[(q==null?void 0:q(l))+"="+(q==null?void 0:q(String($)))]}const ct=[];if(typeof $>"u")return ct;let tt;if(i==="comma"&&me($))Q&&A&&($=Um($,A)),tt=[{value:$.length>0?$.join(",")||null:void 0}];else if(me(v))tt=v;else{const bt=Object.keys($);tt=R?bt.sort(R):bt}const Rt=b?String(l).replace(/\./g,"%2E"):String(l),mt=s&&me($)&&$.length===1?Rt+"[]":Rt;if(f&&me($)&&$.length===0)return mt+"[]";for(let bt=0;bt<tt.length;++bt){const ot=tt[bt],zt=typeof ot=="object"&&typeof ot.value<"u"?ot.value:$[ot];if(m&&zt===null)continue;const je=M&&b?ot.replace(/\./g,"%2E"):ot,Ie=me($)?typeof i=="function"?i(mt,je):mt:mt+(M?"."+je:"["+je+"]");Et.set(r,V);const Gt=new WeakMap;Gt.set(Hr,Et),vy(ct,py(zt,Ie,i,s,f,d,m,b,i==="comma"&&Q&&me($)?null:A,v,R,M,U,C,q,Q,Z,Gt))}return ct}function Mv(r=$t){if(typeof r.allowEmptyArrays<"u"&&typeof r.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof r.encodeDotInKeys<"u"&&typeof r.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(r.encoder!==null&&typeof r.encoder<"u"&&typeof r.encoder!="function")throw new TypeError("Encoder has to be a function.");const l=r.charset||$t.charset;if(typeof r.charset<"u"&&r.charset!=="utf-8"&&r.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let i=yy;if(typeof r.format<"u"){if(!$r(zm,r.format))throw new TypeError("Unknown format option provided.");i=r.format}const s=zm[i];let f=$t.filter;(typeof r.filter=="function"||me(r.filter))&&(f=r.filter);let d;if(r.arrayFormat&&r.arrayFormat in _y?d=r.arrayFormat:"indices"in r?d=r.indices?"indices":"repeat":d=$t.arrayFormat,"commaRoundTrip"in r&&typeof r.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const m=typeof r.allowDots>"u"?r.encodeDotInKeys?!0:$t.allowDots:!!r.allowDots;return{addQueryPrefix:typeof r.addQueryPrefix=="boolean"?r.addQueryPrefix:$t.addQueryPrefix,allowDots:m,allowEmptyArrays:typeof r.allowEmptyArrays=="boolean"?!!r.allowEmptyArrays:$t.allowEmptyArrays,arrayFormat:d,charset:l,charsetSentinel:typeof r.charsetSentinel=="boolean"?r.charsetSentinel:$t.charsetSentinel,commaRoundTrip:!!r.commaRoundTrip,delimiter:typeof r.delimiter>"u"?$t.delimiter:r.delimiter,encode:typeof r.encode=="boolean"?r.encode:$t.encode,encodeDotInKeys:typeof r.encodeDotInKeys=="boolean"?r.encodeDotInKeys:$t.encodeDotInKeys,encoder:typeof r.encoder=="function"?r.encoder:$t.encoder,encodeValuesOnly:typeof r.encodeValuesOnly=="boolean"?r.encodeValuesOnly:$t.encodeValuesOnly,filter:f,format:i,formatter:s,serializeDate:typeof r.serializeDate=="function"?r.serializeDate:$t.serializeDate,skipNulls:typeof r.skipNulls=="boolean"?r.skipNulls:$t.skipNulls,sort:typeof r.sort=="function"?r.sort:null,strictNullHandling:typeof r.strictNullHandling=="boolean"?r.strictNullHandling:$t.strictNullHandling}}function Nv(r,l={}){let i=r;const s=Mv(l);let f,d;typeof s.filter=="function"?(d=s.filter,i=d("",i)):me(s.filter)&&(d=s.filter,f=d);const m=[];if(typeof i!="object"||i===null)return"";const b=_y[s.arrayFormat],A=b==="comma"&&s.commaRoundTrip;f||(f=Object.keys(i)),s.sort&&f.sort(s.sort);const v=new WeakMap;for(let U=0;U<f.length;++U){const C=f[U];s.skipNulls&&i[C]===null||vy(m,py(i[C],C,b,A,s.allowEmptyArrays,s.strictNullHandling,s.skipNulls,s.encodeDotInKeys,s.encode?s.encoder:null,s.filter,s.sort,s.allowDots,s.serializeDate,s.format,s.formatter,s.encodeValuesOnly,s.charset,v))}const R=m.join(s.delimiter);let M=s.addQueryPrefix===!0?"?":"";return s.charsetSentinel&&(s.charset==="iso-8859-1"?M+="utf8=%26%2310003%3B&":M+="utf8=%E2%9C%93&"),R.length>0?M+R:""}function Dv(r){let l=0;for(const f of r)l+=f.length;const i=new Uint8Array(l);let s=0;for(const f of r)i.set(f,s),s+=f.length;return i}let Hm;function uf(r){let l;return(Hm??(l=new globalThis.TextEncoder,Hm=l.encode.bind(l)))(r)}let qm;function jm(r){let l;return(qm??(l=new globalThis.TextDecoder,qm=l.decode.bind(l)))(r)}var Te,Oe;class ss{constructor(){Te.set(this,void 0),Oe.set(this,void 0),lt(this,Te,new Uint8Array),lt(this,Oe,null)}decode(l){if(l==null)return[];const i=l instanceof ArrayBuffer?new Uint8Array(l):typeof l=="string"?uf(l):l;lt(this,Te,Dv([O(this,Te,"f"),i]));const s=[];let f;for(;(f=Cv(O(this,Te,"f"),O(this,Oe,"f")))!=null;){if(f.carriage&&O(this,Oe,"f")==null){lt(this,Oe,f.index);continue}if(O(this,Oe,"f")!=null&&(f.index!==O(this,Oe,"f")+1||f.carriage)){s.push(jm(O(this,Te,"f").subarray(0,O(this,Oe,"f")-1))),lt(this,Te,O(this,Te,"f").subarray(O(this,Oe,"f"))),lt(this,Oe,null);continue}const d=O(this,Oe,"f")!==null?f.preceding-1:f.preceding,m=jm(O(this,Te,"f").subarray(0,d));s.push(m),lt(this,Te,O(this,Te,"f").subarray(f.index)),lt(this,Oe,null)}return s}flush(){return O(this,Te,"f").length?this.decode(`
`):[]}}Te=new WeakMap,Oe=new WeakMap;ss.NEWLINE_CHARS=new Set([`
`,"\r"]);ss.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function Cv(r,l){for(let f=l??0;f<r.length;f++){if(r[f]===10)return{preceding:f,index:f+1,carriage:!1};if(r[f]===13)return{preceding:f,index:f+1,carriage:!0}}return null}function zv(r){for(let s=0;s<r.length-1;s++){if(r[s]===10&&r[s+1]===10||r[s]===13&&r[s+1]===13)return s+2;if(r[s]===13&&r[s+1]===10&&s+3<r.length&&r[s+2]===13&&r[s+3]===10)return s+4}return-1}class Fe{constructor(l,i){this.iterator=l,this.controller=i}static fromSSEResponse(l,i){let s=!1;async function*f(){if(s)throw new nt("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let d=!1;try{for await(const m of Uv(l,i))if(!d){if(m.data.startsWith("[DONE]")){d=!0;continue}if(m.event===null||m.event.startsWith("response.")||m.event.startsWith("transcript.")){let b;try{b=JSON.parse(m.data)}catch(A){throw console.error("Could not parse message into JSON:",m.data),console.error("From chunk:",m.raw),A}if(b&&b.error)throw new ae(void 0,b.error,void 0,l.headers);yield b}else{let b;try{b=JSON.parse(m.data)}catch(A){throw console.error("Could not parse message into JSON:",m.data),console.error("From chunk:",m.raw),A}if(m.event=="error")throw new ae(void 0,b.error,b.message,void 0);yield{event:m.event,data:b}}}d=!0}catch(m){if(Zr(m))return;throw m}finally{d||i.abort()}}return new Fe(f,i)}static fromReadableStream(l,i){let s=!1;async function*f(){const m=new ss,b=my(l);for await(const A of b)for(const v of m.decode(A))yield v;for(const A of m.flush())yield A}async function*d(){if(s)throw new nt("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let m=!1;try{for await(const b of f())m||b&&(yield JSON.parse(b));m=!0}catch(b){if(Zr(b))return;throw b}finally{m||i.abort()}}return new Fe(d,i)}[Symbol.asyncIterator](){return this.iterator()}tee(){const l=[],i=[],s=this.iterator(),f=d=>({next:()=>{if(d.length===0){const m=s.next();l.push(m),i.push(m)}return d.shift()}});return[new Fe(()=>f(l),this.controller),new Fe(()=>f(i),this.controller)]}toReadableStream(){const l=this;let i;return dy({async start(){i=l[Symbol.asyncIterator]()},async pull(s){try{const{value:f,done:d}=await i.next();if(d)return s.close();const m=uf(JSON.stringify(f)+`
`);s.enqueue(m)}catch(f){s.error(f)}},async cancel(){var s;await((s=i.return)==null?void 0:s.call(i))}})}}async function*Uv(r,l){if(!r.body)throw l.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new nt("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new nt("Attempted to iterate over a response with no body");const i=new Hv,s=new ss,f=my(r.body);for await(const d of Bv(f))for(const m of s.decode(d)){const b=i.decode(m);b&&(yield b)}for(const d of s.flush()){const m=i.decode(d);m&&(yield m)}}async function*Bv(r){let l=new Uint8Array;for await(const i of r){if(i==null)continue;const s=i instanceof ArrayBuffer?new Uint8Array(i):typeof i=="string"?uf(i):i;let f=new Uint8Array(l.length+s.length);f.set(l),f.set(s,l.length),l=f;let d;for(;(d=zv(l))!==-1;)yield l.slice(0,d),l=l.slice(d)}l.length>0&&(yield l)}class Hv{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(l){if(l.endsWith("\r")&&(l=l.substring(0,l.length-1)),!l){if(!this.event&&!this.data.length)return null;const d={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],d}if(this.chunks.push(l),l.startsWith(":"))return null;let[i,s,f]=qv(l,":");return f.startsWith(" ")&&(f=f.substring(1)),i==="event"?this.event=f:i==="data"&&this.data.push(f),null}}function qv(r,l){const i=r.indexOf(l);return i!==-1?[r.substring(0,i),l,r.substring(i+l.length)]:[r,"",""]}const Pi={off:0,error:200,warn:300,info:400,debug:500},Lm=(r,l,i)=>{if(r){if(mv(Pi,r))return r;ie(i).warn(`${l} was set to ${JSON.stringify(r)}, expected one of ${JSON.stringify(Object.keys(Pi))}`)}};function iu(){}function qi(r,l,i){return!l||Pi[r]>Pi[i]?iu:l[r].bind(l)}const jv={error:iu,warn:iu,info:iu,debug:iu};let Ym=new WeakMap;function ie(r){const l=r.logger,i=r.logLevel??"off";if(!l)return jv;const s=Ym.get(l);if(s&&s[0]===i)return s[1];const f={error:qi("error",l,i),warn:qi("warn",l,i),info:qi("info",l,i),debug:qi("debug",l,i)};return Ym.set(l,[i,f]),f}const rn=r=>(r.options&&(r.options={...r.options},delete r.options.headers),r.headers&&(r.headers=Object.fromEntries((r.headers instanceof Headers?[...r.headers]:Object.entries(r.headers)).map(([l,i])=>[l,l.toLowerCase()==="authorization"||l.toLowerCase()==="cookie"||l.toLowerCase()==="set-cookie"?"***":i]))),"retryOfRequestLogID"in r&&(r.retryOfRequestLogID&&(r.retryOf=r.retryOfRequestLogID),delete r.retryOfRequestLogID),r);async function by(r,l){const{response:i,requestLogID:s,retryOfRequestLogID:f,startTime:d}=l,m=await(async()=>{var M;if(l.options.stream)return ie(r).debug("response",i.status,i.url,i.headers,i.body),l.options.__streamClass?l.options.__streamClass.fromSSEResponse(i,l.controller):Fe.fromSSEResponse(i,l.controller);if(i.status===204)return null;if(l.options.__binaryResponse)return i;const b=i.headers.get("content-type"),A=(M=b==null?void 0:b.split(";")[0])==null?void 0:M.trim();if((A==null?void 0:A.includes("application/json"))||(A==null?void 0:A.endsWith("+json"))){const U=await i.json();return Sy(U,i)}return await i.text()})();return ie(r).debug(`[${s}] response parsed`,rn({retryOfRequestLogID:f,url:i.url,status:i.status,body:m,durationMs:Date.now()-d})),m}function Sy(r,l){return!r||typeof r!="object"||Array.isArray(r)?r:Object.defineProperty(r,"_request_id",{value:l.headers.get("x-request-id"),enumerable:!1})}var su;class cs extends Promise{constructor(l,i,s=by){super(f=>{f(null)}),this.responsePromise=i,this.parseResponse=s,su.set(this,void 0),lt(this,su,l)}_thenUnwrap(l){return new cs(O(this,su,"f"),this.responsePromise,async(i,s)=>Sy(l(await this.parseResponse(i,s),s),s.response))}asResponse(){return this.responsePromise.then(l=>l.response)}async withResponse(){const[l,i]=await Promise.all([this.parse(),this.asResponse()]);return{data:l,response:i,request_id:i.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(l=>this.parseResponse(O(this,su,"f"),l))),this.parsedPromise}then(l,i){return this.parse().then(l,i)}catch(l){return this.parse().catch(l)}finally(l){return this.parse().finally(l)}}su=new WeakMap;var ji;class Ay{constructor(l,i,s,f){ji.set(this,void 0),lt(this,ji,l),this.options=f,this.response=i,this.body=s}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const l=this.nextPageRequestOptions();if(!l)throw new nt("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await O(this,ji,"f").requestAPIList(this.constructor,l)}async*iterPages(){let l=this;for(yield l;l.hasNextPage();)l=await l.getNextPage(),yield l}async*[(ji=new WeakMap,Symbol.asyncIterator)](){for await(const l of this.iterPages())for(const i of l.getPaginatedItems())yield i}}class Lv extends cs{constructor(l,i,s){super(l,i,async(f,d)=>new s(f,d.response,await by(f,d),d.options))}async*[Symbol.asyncIterator](){const l=await this;for await(const i of l)yield i}}class rs extends Ay{constructor(l,i,s,f){super(l,i,s,f),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class Lt extends Ay{constructor(l,i,s,f){super(l,i,s,f),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var s;const l=this.getPaginatedItems(),i=(s=l[l.length-1])==null?void 0:s.id;return i?{...this.options,query:{...dv(this.options.query),after:i}}:null}}const Ey=()=>{var r;if(typeof File>"u"){const{process:l}=globalThis,i=typeof((r=l==null?void 0:l.versions)==null?void 0:r.node)=="string"&&parseInt(l.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(i?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function mu(r,l,i){return Ey(),new File(r,l??"unknown_file",i)}function Zi(r){return(typeof r=="object"&&r!==null&&("name"in r&&r.name&&String(r.name)||"url"in r&&r.url&&String(r.url)||"filename"in r&&r.filename&&String(r.filename)||"path"in r&&r.path&&String(r.path))||"").split(/[\\/]/).pop()||void 0}const wy=r=>r!=null&&typeof r=="object"&&typeof r[Symbol.asyncIterator]=="function",mn=async(r,l)=>({...r,body:await Xv(r.body,l)}),Xm=new WeakMap;function Yv(r){const l=typeof r=="function"?r:r.fetch,i=Xm.get(l);if(i)return i;const s=(async()=>{try{const f="Response"in l?l.Response:(await l("data:,")).constructor,d=new FormData;return d.toString()!==await new f(d).text()}catch{return!0}})();return Xm.set(l,s),s}const Xv=async(r,l)=>{if(!await Yv(l))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const i=new FormData;return await Promise.all(Object.entries(r||{}).map(([s,f])=>Kr(i,s,f))),i},Gv=r=>r instanceof Blob&&"name"in r,Kr=async(r,l,i)=>{if(i!==void 0){if(i==null)throw new TypeError(`Received null for "${l}"; to pass null in FormData, you must use the string 'null'`);if(typeof i=="string"||typeof i=="number"||typeof i=="boolean")r.append(l,String(i));else if(i instanceof Response)r.append(l,mu([await i.blob()],Zi(i)));else if(wy(i))r.append(l,mu([await new Response(hy(i)).blob()],Zi(i)));else if(Gv(i))r.append(l,i,Zi(i));else if(Array.isArray(i))await Promise.all(i.map(s=>Kr(r,l+"[]",s)));else if(typeof i=="object")await Promise.all(Object.entries(i).map(([s,f])=>Kr(r,`${l}[${s}]`,f)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${i} instead`)}},Ty=r=>r!=null&&typeof r=="object"&&typeof r.size=="number"&&typeof r.type=="string"&&typeof r.text=="function"&&typeof r.slice=="function"&&typeof r.arrayBuffer=="function",Qv=r=>r!=null&&typeof r=="object"&&typeof r.name=="string"&&typeof r.lastModified=="number"&&Ty(r),Zv=r=>r!=null&&typeof r=="object"&&typeof r.url=="string"&&typeof r.blob=="function";async function Vv(r,l,i){if(Ey(),r=await r,Qv(r))return r instanceof File?r:mu([await r.arrayBuffer()],r.name);if(Zv(r)){const f=await r.blob();return l||(l=new URL(r.url).pathname.split(/[\\/]/).pop()),mu(await Jr(f),l,i)}const s=await Jr(r);if(l||(l=Zi(r)),!(i!=null&&i.type)){const f=s.find(d=>typeof d=="object"&&"type"in d&&d.type);typeof f=="string"&&(i={...i,type:f})}return mu(s,l,i)}async function Jr(r){var i;let l=[];if(typeof r=="string"||ArrayBuffer.isView(r)||r instanceof ArrayBuffer)l.push(r);else if(Ty(r))l.push(r instanceof Blob?r:await r.arrayBuffer());else if(wy(r))for await(const s of r)l.push(...await Jr(s));else{const s=(i=r==null?void 0:r.constructor)==null?void 0:i.name;throw new Error(`Unexpected data type: ${typeof r}${s?`; constructor: ${s}`:""}${$v(r)}`)}return l}function $v(r){return typeof r!="object"||r===null?"":`; props: [${Object.getOwnPropertyNames(r).map(i=>`"${i}"`).join(", ")}]`}class ut{constructor(l){this._client=l}}function Oy(r){return r.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const Kv=(r=Oy)=>function(i,...s){if(i.length===1)return i[0];let f=!1;const d=i.reduce((R,M,U)=>(/[?#]/.test(M)&&(f=!0),R+M+(U===s.length?"":(f?encodeURIComponent:r)(String(s[U])))),""),m=d.split(/[?#]/,1)[0],b=[],A=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let v;for(;(v=A.exec(m))!==null;)b.push({start:v.index,length:v[0].length});if(b.length>0){let R=0;const M=b.reduce((U,C)=>{const q=" ".repeat(C.start-R),Q="^".repeat(C.length);return R=C.start+C.length,U+q+Q},"");throw new nt(`Path parameters result in path with invalid segments:
${d}
${M}`)}return d},H=Kv(Oy);let Ry=class extends ut{list(l,i={},s){return this._client.getAPIList(H`/chat/completions/${l}/messages`,Lt,{query:i,...s})}};function Jv(r){return typeof r.parse=="function"}const ts=r=>(r==null?void 0:r.role)==="assistant",xy=r=>(r==null?void 0:r.role)==="tool";var kr,Vi,$i,cu,ru,Ki,fu,ya,ou,es,as,nl,My;class sf{constructor(){kr.add(this),this.controller=new AbortController,Vi.set(this,void 0),$i.set(this,()=>{}),cu.set(this,()=>{}),ru.set(this,void 0),Ki.set(this,()=>{}),fu.set(this,()=>{}),ya.set(this,{}),ou.set(this,!1),es.set(this,!1),as.set(this,!1),nl.set(this,!1),lt(this,Vi,new Promise((l,i)=>{lt(this,$i,l,"f"),lt(this,cu,i,"f")})),lt(this,ru,new Promise((l,i)=>{lt(this,Ki,l,"f"),lt(this,fu,i,"f")})),O(this,Vi,"f").catch(()=>{}),O(this,ru,"f").catch(()=>{})}_run(l){setTimeout(()=>{l().then(()=>{this._emitFinal(),this._emit("end")},O(this,kr,"m",My).bind(this))},0)}_connected(){this.ended||(O(this,$i,"f").call(this),this._emit("connect"))}get ended(){return O(this,ou,"f")}get errored(){return O(this,es,"f")}get aborted(){return O(this,as,"f")}abort(){this.controller.abort()}on(l,i){return(O(this,ya,"f")[l]||(O(this,ya,"f")[l]=[])).push({listener:i}),this}off(l,i){const s=O(this,ya,"f")[l];if(!s)return this;const f=s.findIndex(d=>d.listener===i);return f>=0&&s.splice(f,1),this}once(l,i){return(O(this,ya,"f")[l]||(O(this,ya,"f")[l]=[])).push({listener:i,once:!0}),this}emitted(l){return new Promise((i,s)=>{lt(this,nl,!0),l!=="error"&&this.once("error",s),this.once(l,i)})}async done(){lt(this,nl,!0),await O(this,ru,"f")}_emit(l,...i){if(O(this,ou,"f"))return;l==="end"&&(lt(this,ou,!0),O(this,Ki,"f").call(this));const s=O(this,ya,"f")[l];if(s&&(O(this,ya,"f")[l]=s.filter(f=>!f.once),s.forEach(({listener:f})=>f(...i))),l==="abort"){const f=i[0];!O(this,nl,"f")&&!(s!=null&&s.length)&&Promise.reject(f),O(this,cu,"f").call(this,f),O(this,fu,"f").call(this,f),this._emit("end");return}if(l==="error"){const f=i[0];!O(this,nl,"f")&&!(s!=null&&s.length)&&Promise.reject(f),O(this,cu,"f").call(this,f),O(this,fu,"f").call(this,f),this._emit("end")}}_emitFinal(){}}Vi=new WeakMap,$i=new WeakMap,cu=new WeakMap,ru=new WeakMap,Ki=new WeakMap,fu=new WeakMap,ya=new WeakMap,ou=new WeakMap,es=new WeakMap,as=new WeakMap,nl=new WeakMap,kr=new WeakSet,My=function(l){if(lt(this,es,!0),l instanceof Error&&l.name==="AbortError"&&(l=new qe),l instanceof qe)return lt(this,as,!0),this._emit("abort",l);if(l instanceof nt)return this._emit("error",l);if(l instanceof Error){const i=new nt(l.message);return i.cause=l,this._emit("error",i)}return this._emit("error",new nt(String(l)))};function cf(r){return(r==null?void 0:r.$brand)==="auto-parseable-response-format"}function pu(r){return(r==null?void 0:r.$brand)==="auto-parseable-tool"}function kv(r,l){return!l||!Ny(l)?{...r,choices:r.choices.map(i=>({...i,message:{...i.message,parsed:null,...i.message.tool_calls?{tool_calls:i.message.tool_calls}:void 0}}))}:rf(r,l)}function rf(r,l){const i=r.choices.map(s=>{var f;if(s.finish_reason==="length")throw new fy;if(s.finish_reason==="content_filter")throw new oy;return{...s,message:{...s.message,...s.message.tool_calls?{tool_calls:((f=s.message.tool_calls)==null?void 0:f.map(d=>Fv(l,d)))??void 0}:void 0,parsed:s.message.content&&!s.message.refusal?Wv(l,s.message.content):null}}});return{...r,choices:i}}function Wv(r,l){var i,s;return((i=r.response_format)==null?void 0:i.type)!=="json_schema"?null:((s=r.response_format)==null?void 0:s.type)==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(l):JSON.parse(l):null}function Fv(r,l){var s;const i=(s=r.tools)==null?void 0:s.find(f=>{var d;return((d=f.function)==null?void 0:d.name)===l.function.name});return{...l,function:{...l.function,parsed_arguments:pu(i)?i.$parseRaw(l.function.arguments):i!=null&&i.function.strict?JSON.parse(l.function.arguments):null}}}function Iv(r,l){var s;if(!r)return!1;const i=(s=r.tools)==null?void 0:s.find(f=>{var d;return((d=f.function)==null?void 0:d.name)===l.function.name});return pu(i)||(i==null?void 0:i.function.strict)||!1}function Ny(r){var l;return cf(r.response_format)?!0:((l=r.tools)==null?void 0:l.some(i=>pu(i)||i.type==="function"&&i.function.strict===!0))??!1}function Pv(r){for(const l of r??[]){if(l.type!=="function")throw new nt(`Currently only \`function\` tool types support auto-parsing; Received \`${l.type}\``);if(l.function.strict!==!0)throw new nt(`The \`${l.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var ce,Wr,ns,Fr,Ir,Pr,Dy,Cy;const tp=10;class zy extends sf{constructor(){super(...arguments),ce.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(l){var s;this._chatCompletions.push(l),this._emit("chatCompletion",l);const i=(s=l.choices[0])==null?void 0:s.message;return i&&this._addMessage(i),l}_addMessage(l,i=!0){if("content"in l||(l.content=null),this.messages.push(l),i){if(this._emit("message",l),xy(l)&&l.content)this._emit("functionToolCallResult",l.content);else if(ts(l)&&l.tool_calls)for(const s of l.tool_calls)s.type==="function"&&this._emit("functionToolCall",s.function)}}async finalChatCompletion(){await this.done();const l=this._chatCompletions[this._chatCompletions.length-1];if(!l)throw new nt("stream ended without producing a ChatCompletion");return l}async finalContent(){return await this.done(),O(this,ce,"m",Wr).call(this)}async finalMessage(){return await this.done(),O(this,ce,"m",ns).call(this)}async finalFunctionToolCall(){return await this.done(),O(this,ce,"m",Fr).call(this)}async finalFunctionToolCallResult(){return await this.done(),O(this,ce,"m",Ir).call(this)}async totalUsage(){return await this.done(),O(this,ce,"m",Pr).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const l=this._chatCompletions[this._chatCompletions.length-1];l&&this._emit("finalChatCompletion",l);const i=O(this,ce,"m",ns).call(this);i&&this._emit("finalMessage",i);const s=O(this,ce,"m",Wr).call(this);s&&this._emit("finalContent",s);const f=O(this,ce,"m",Fr).call(this);f&&this._emit("finalFunctionToolCall",f);const d=O(this,ce,"m",Ir).call(this);d!=null&&this._emit("finalFunctionToolCallResult",d),this._chatCompletions.some(m=>m.usage)&&this._emit("totalUsage",O(this,ce,"m",Pr).call(this))}async _createChatCompletion(l,i,s){const f=s==null?void 0:s.signal;f&&(f.aborted&&this.controller.abort(),f.addEventListener("abort",()=>this.controller.abort())),O(this,ce,"m",Dy).call(this,i);const d=await l.chat.completions.create({...i,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(rf(d,i))}async _runChatCompletion(l,i,s){for(const f of i.messages)this._addMessage(f,!1);return await this._createChatCompletion(l,i,s)}async _runTools(l,i,s){var C,q,Q;const f="tool",{tool_choice:d="auto",stream:m,...b}=i,A=typeof d!="string"&&((C=d==null?void 0:d.function)==null?void 0:C.name),{maxChatCompletions:v=tp}=s||{},R=i.tools.map(Z=>{if(pu(Z)){if(!Z.$callback)throw new nt("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:Z.$callback,name:Z.function.name,description:Z.function.description||"",parameters:Z.function.parameters,parse:Z.$parseRaw,strict:!0}}}return Z}),M={};for(const Z of R)Z.type==="function"&&(M[Z.function.name||Z.function.function.name]=Z.function);const U="tools"in i?R.map(Z=>Z.type==="function"?{type:"function",function:{name:Z.function.name||Z.function.function.name,parameters:Z.function.parameters,description:Z.function.description,strict:Z.function.strict}}:Z):void 0;for(const Z of i.messages)this._addMessage(Z,!1);for(let Z=0;Z<v;++Z){const $=(q=(await this._createChatCompletion(l,{...b,tool_choice:d,tools:U,messages:[...this.messages]},s)).choices[0])==null?void 0:q.message;if(!$)throw new nt("missing message in ChatCompletion response");if(!((Q=$.tool_calls)!=null&&Q.length))return;for(const J of $.tool_calls){if(J.type!=="function")continue;const V=J.id,{name:k,arguments:ct}=J.function,tt=M[k];if(tt){if(A&&A!==k){const ot=`Invalid tool_call: ${JSON.stringify(k)}. ${JSON.stringify(A)} requested. Please try again`;this._addMessage({role:f,tool_call_id:V,content:ot});continue}}else{const ot=`Invalid tool_call: ${JSON.stringify(k)}. Available options are: ${Object.keys(M).map(zt=>JSON.stringify(zt)).join(", ")}. Please try again`;this._addMessage({role:f,tool_call_id:V,content:ot});continue}let Rt;try{Rt=Jv(tt)?await tt.parse(ct):ct}catch(ot){const zt=ot instanceof Error?ot.message:String(ot);this._addMessage({role:f,tool_call_id:V,content:zt});continue}const mt=await tt.function(Rt,this),bt=O(this,ce,"m",Cy).call(this,mt);if(this._addMessage({role:f,tool_call_id:V,content:bt}),A)return}}}}ce=new WeakSet,Wr=function(){return O(this,ce,"m",ns).call(this).content??null},ns=function(){let l=this.messages.length;for(;l-- >0;){const i=this.messages[l];if(ts(i))return{...i,content:i.content??null,refusal:i.refusal??null}}throw new nt("stream ended without producing a ChatCompletionMessage with role=assistant")},Fr=function(){var l,i;for(let s=this.messages.length-1;s>=0;s--){const f=this.messages[s];if(ts(f)&&((l=f==null?void 0:f.tool_calls)!=null&&l.length))return(i=f.tool_calls.at(-1))==null?void 0:i.function}},Ir=function(){for(let l=this.messages.length-1;l>=0;l--){const i=this.messages[l];if(xy(i)&&i.content!=null&&typeof i.content=="string"&&this.messages.some(s=>{var f;return s.role==="assistant"&&((f=s.tool_calls)==null?void 0:f.some(d=>d.type==="function"&&d.id===i.tool_call_id))}))return i.content}},Pr=function(){const l={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:i}of this._chatCompletions)i&&(l.completion_tokens+=i.completion_tokens,l.prompt_tokens+=i.prompt_tokens,l.total_tokens+=i.total_tokens);return l},Dy=function(l){if(l.n!=null&&l.n>1)throw new nt("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},Cy=function(l){return typeof l=="string"?l:l===void 0?"undefined":JSON.stringify(l)};class ff extends zy{static runTools(l,i,s){const f=new ff,d={...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return f._run(()=>f._runTools(l,i,d)),f}_addMessage(l,i=!0){super._addMessage(l,i),ts(l)&&l.content&&this._emit("content",l.content)}}const Uy=1,By=2,Hy=4,qy=8,jy=16,Ly=32,Yy=64,Xy=128,Gy=256,Qy=Xy|Gy,Zy=jy|Ly|Qy|Yy,Vy=Uy|By|Zy,$y=Hy|qy,ep=Vy|$y,Ft={STR:Uy,NUM:By,ARR:Hy,OBJ:qy,NULL:jy,BOOL:Ly,NAN:Yy,INFINITY:Xy,MINUS_INFINITY:Gy,INF:Qy,SPECIAL:Zy,ATOM:Vy,COLLECTION:$y,ALL:ep};class ap extends Error{}class np extends Error{}function lp(r,l=Ft.ALL){if(typeof r!="string")throw new TypeError(`expecting str, got ${typeof r}`);if(!r.trim())throw new Error(`${r} is empty`);return up(r.trim(),l)}const up=(r,l)=>{const i=r.length;let s=0;const f=U=>{throw new ap(`${U} at position ${s}`)},d=U=>{throw new np(`${U} at position ${s}`)},m=()=>(M(),s>=i&&f("Unexpected end of input"),r[s]==='"'?b():r[s]==="{"?A():r[s]==="["?v():r.substring(s,s+4)==="null"||Ft.NULL&l&&i-s<4&&"null".startsWith(r.substring(s))?(s+=4,null):r.substring(s,s+4)==="true"||Ft.BOOL&l&&i-s<4&&"true".startsWith(r.substring(s))?(s+=4,!0):r.substring(s,s+5)==="false"||Ft.BOOL&l&&i-s<5&&"false".startsWith(r.substring(s))?(s+=5,!1):r.substring(s,s+8)==="Infinity"||Ft.INFINITY&l&&i-s<8&&"Infinity".startsWith(r.substring(s))?(s+=8,1/0):r.substring(s,s+9)==="-Infinity"||Ft.MINUS_INFINITY&l&&1<i-s&&i-s<9&&"-Infinity".startsWith(r.substring(s))?(s+=9,-1/0):r.substring(s,s+3)==="NaN"||Ft.NAN&l&&i-s<3&&"NaN".startsWith(r.substring(s))?(s+=3,NaN):R()),b=()=>{const U=s;let C=!1;for(s++;s<i&&(r[s]!=='"'||C&&r[s-1]==="\\");)C=r[s]==="\\"?!C:!1,s++;if(r.charAt(s)=='"')try{return JSON.parse(r.substring(U,++s-Number(C)))}catch(q){d(String(q))}else if(Ft.STR&l)try{return JSON.parse(r.substring(U,s-Number(C))+'"')}catch{return JSON.parse(r.substring(U,r.lastIndexOf("\\"))+'"')}f("Unterminated string literal")},A=()=>{s++,M();const U={};try{for(;r[s]!=="}";){if(M(),s>=i&&Ft.OBJ&l)return U;const C=b();M(),s++;try{const q=m();Object.defineProperty(U,C,{value:q,writable:!0,enumerable:!0,configurable:!0})}catch(q){if(Ft.OBJ&l)return U;throw q}M(),r[s]===","&&s++}}catch{if(Ft.OBJ&l)return U;f("Expected '}' at end of object")}return s++,U},v=()=>{s++;const U=[];try{for(;r[s]!=="]";)U.push(m()),M(),r[s]===","&&s++}catch{if(Ft.ARR&l)return U;f("Expected ']' at end of array")}return s++,U},R=()=>{if(s===0){r==="-"&&Ft.NUM&l&&f("Not sure what '-' is");try{return JSON.parse(r)}catch(C){if(Ft.NUM&l)try{return r[r.length-1]==="."?JSON.parse(r.substring(0,r.lastIndexOf("."))):JSON.parse(r.substring(0,r.lastIndexOf("e")))}catch{}d(String(C))}}const U=s;for(r[s]==="-"&&s++;r[s]&&!",]}".includes(r[s]);)s++;s==i&&!(Ft.NUM&l)&&f("Unterminated number literal");try{return JSON.parse(r.substring(U,s))}catch{r.substring(U,s)==="-"&&Ft.NUM&l&&f("Not sure what '-' is");try{return JSON.parse(r.substring(U,r.lastIndexOf("e")))}catch(q){d(String(q))}}},M=()=>{for(;s<i&&` 
\r	`.includes(r[s]);)s++};return m()},Gm=r=>lp(r,Ft.ALL^Ft.NUM);var Vt,ma,tl,Ga,qr,Li,jr,Lr,Yr,Yi,Xr,Qm;class _u extends zy{constructor(l){super(),Vt.add(this),ma.set(this,void 0),tl.set(this,void 0),Ga.set(this,void 0),lt(this,ma,l),lt(this,tl,[])}get currentChatCompletionSnapshot(){return O(this,Ga,"f")}static fromReadableStream(l){const i=new _u(null);return i._run(()=>i._fromReadableStream(l)),i}static createChatCompletion(l,i,s){const f=new _u(i);return f._run(()=>f._runChatCompletion(l,{...i,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),f}async _createChatCompletion(l,i,s){var m;super._createChatCompletion;const f=s==null?void 0:s.signal;f&&(f.aborted&&this.controller.abort(),f.addEventListener("abort",()=>this.controller.abort())),O(this,Vt,"m",qr).call(this);const d=await l.chat.completions.create({...i,stream:!0},{...s,signal:this.controller.signal});this._connected();for await(const b of d)O(this,Vt,"m",jr).call(this,b);if((m=d.controller.signal)!=null&&m.aborted)throw new qe;return this._addChatCompletion(O(this,Vt,"m",Yi).call(this))}async _fromReadableStream(l,i){var m;const s=i==null?void 0:i.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),O(this,Vt,"m",qr).call(this),this._connected();const f=Fe.fromReadableStream(l,this.controller);let d;for await(const b of f)d&&d!==b.id&&this._addChatCompletion(O(this,Vt,"m",Yi).call(this)),O(this,Vt,"m",jr).call(this,b),d=b.id;if((m=f.controller.signal)!=null&&m.aborted)throw new qe;return this._addChatCompletion(O(this,Vt,"m",Yi).call(this))}[(ma=new WeakMap,tl=new WeakMap,Ga=new WeakMap,Vt=new WeakSet,qr=function(){this.ended||lt(this,Ga,void 0)},Li=function(i){let s=O(this,tl,"f")[i.index];return s||(s={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},O(this,tl,"f")[i.index]=s,s)},jr=function(i){var f,d,m,b,A,v,R,M,U,C,q,Q,Z,Et,$;if(this.ended)return;const s=O(this,Vt,"m",Qm).call(this,i);this._emit("chunk",i,s);for(const J of i.choices){const V=s.choices[J.index];J.delta.content!=null&&((f=V.message)==null?void 0:f.role)==="assistant"&&((d=V.message)!=null&&d.content)&&(this._emit("content",J.delta.content,V.message.content),this._emit("content.delta",{delta:J.delta.content,snapshot:V.message.content,parsed:V.message.parsed})),J.delta.refusal!=null&&((m=V.message)==null?void 0:m.role)==="assistant"&&((b=V.message)!=null&&b.refusal)&&this._emit("refusal.delta",{delta:J.delta.refusal,snapshot:V.message.refusal}),((A=J.logprobs)==null?void 0:A.content)!=null&&((v=V.message)==null?void 0:v.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(R=J.logprobs)==null?void 0:R.content,snapshot:((M=V.logprobs)==null?void 0:M.content)??[]}),((U=J.logprobs)==null?void 0:U.refusal)!=null&&((C=V.message)==null?void 0:C.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(q=J.logprobs)==null?void 0:q.refusal,snapshot:((Q=V.logprobs)==null?void 0:Q.refusal)??[]});const k=O(this,Vt,"m",Li).call(this,V);V.finish_reason&&(O(this,Vt,"m",Yr).call(this,V),k.current_tool_call_index!=null&&O(this,Vt,"m",Lr).call(this,V,k.current_tool_call_index));for(const ct of J.delta.tool_calls??[])k.current_tool_call_index!==ct.index&&(O(this,Vt,"m",Yr).call(this,V),k.current_tool_call_index!=null&&O(this,Vt,"m",Lr).call(this,V,k.current_tool_call_index)),k.current_tool_call_index=ct.index;for(const ct of J.delta.tool_calls??[]){const tt=(Z=V.message.tool_calls)==null?void 0:Z[ct.index];tt!=null&&tt.type&&((tt==null?void 0:tt.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(Et=tt.function)==null?void 0:Et.name,index:ct.index,arguments:tt.function.arguments,parsed_arguments:tt.function.parsed_arguments,arguments_delta:(($=ct.function)==null?void 0:$.arguments)??""}):(tt==null||tt.type,void 0))}}},Lr=function(i,s){var m,b,A;if(O(this,Vt,"m",Li).call(this,i).done_tool_calls.has(s))return;const d=(m=i.message.tool_calls)==null?void 0:m[s];if(!d)throw new Error("no tool call snapshot");if(!d.type)throw new Error("tool call snapshot missing `type`");if(d.type==="function"){const v=(A=(b=O(this,ma,"f"))==null?void 0:b.tools)==null?void 0:A.find(R=>R.type==="function"&&R.function.name===d.function.name);this._emit("tool_calls.function.arguments.done",{name:d.function.name,index:s,arguments:d.function.arguments,parsed_arguments:pu(v)?v.$parseRaw(d.function.arguments):v!=null&&v.function.strict?JSON.parse(d.function.arguments):null})}else d.type},Yr=function(i){var f,d;const s=O(this,Vt,"m",Li).call(this,i);if(i.message.content&&!s.content_done){s.content_done=!0;const m=O(this,Vt,"m",Xr).call(this);this._emit("content.done",{content:i.message.content,parsed:m?m.$parseRaw(i.message.content):null})}i.message.refusal&&!s.refusal_done&&(s.refusal_done=!0,this._emit("refusal.done",{refusal:i.message.refusal})),(f=i.logprobs)!=null&&f.content&&!s.logprobs_content_done&&(s.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:i.logprobs.content})),(d=i.logprobs)!=null&&d.refusal&&!s.logprobs_refusal_done&&(s.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:i.logprobs.refusal}))},Yi=function(){if(this.ended)throw new nt("stream has ended, this shouldn't happen");const i=O(this,Ga,"f");if(!i)throw new nt("request ended without sending any chunks");return lt(this,Ga,void 0),lt(this,tl,[]),ip(i,O(this,ma,"f"))},Xr=function(){var s;const i=(s=O(this,ma,"f"))==null?void 0:s.response_format;return cf(i)?i:null},Qm=function(i){var s,f,d,m;let b=O(this,Ga,"f");const{choices:A,...v}=i;b?Object.assign(b,v):b=lt(this,Ga,{...v,choices:[]});for(const{delta:R,finish_reason:M,index:U,logprobs:C=null,...q}of i.choices){let Q=b.choices[U];if(Q||(Q=b.choices[U]={finish_reason:M,index:U,message:{},logprobs:C,...q}),C)if(!Q.logprobs)Q.logprobs=Object.assign({},C);else{const{content:ct,refusal:tt,...Rt}=C;Object.assign(Q.logprobs,Rt),ct&&((s=Q.logprobs).content??(s.content=[]),Q.logprobs.content.push(...ct)),tt&&((f=Q.logprobs).refusal??(f.refusal=[]),Q.logprobs.refusal.push(...tt))}if(M&&(Q.finish_reason=M,O(this,ma,"f")&&Ny(O(this,ma,"f")))){if(M==="length")throw new fy;if(M==="content_filter")throw new oy}if(Object.assign(Q,q),!R)continue;const{content:Z,refusal:Et,function_call:$,role:J,tool_calls:V,...k}=R;if(Object.assign(Q.message,k),Et&&(Q.message.refusal=(Q.message.refusal||"")+Et),J&&(Q.message.role=J),$&&(Q.message.function_call?($.name&&(Q.message.function_call.name=$.name),$.arguments&&((d=Q.message.function_call).arguments??(d.arguments=""),Q.message.function_call.arguments+=$.arguments)):Q.message.function_call=$),Z&&(Q.message.content=(Q.message.content||"")+Z,!Q.message.refusal&&O(this,Vt,"m",Xr).call(this)&&(Q.message.parsed=Gm(Q.message.content))),V){Q.message.tool_calls||(Q.message.tool_calls=[]);for(const{index:ct,id:tt,type:Rt,function:mt,...bt}of V){const ot=(m=Q.message.tool_calls)[ct]??(m[ct]={});Object.assign(ot,bt),tt&&(ot.id=tt),Rt&&(ot.type=Rt),mt&&(ot.function??(ot.function={name:mt.name??"",arguments:""})),mt!=null&&mt.name&&(ot.function.name=mt.name),mt!=null&&mt.arguments&&(ot.function.arguments+=mt.arguments,Iv(O(this,ma,"f"),ot)&&(ot.function.parsed_arguments=Gm(ot.function.arguments)))}}}return b},Symbol.asyncIterator)](){const l=[],i=[];let s=!1;return this.on("chunk",f=>{const d=i.shift();d?d.resolve(f):l.push(f)}),this.on("end",()=>{s=!0;for(const f of i)f.resolve(void 0);i.length=0}),this.on("abort",f=>{s=!0;for(const d of i)d.reject(f);i.length=0}),this.on("error",f=>{s=!0;for(const d of i)d.reject(f);i.length=0}),{next:async()=>l.length?{value:l.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((d,m)=>i.push({resolve:d,reject:m})).then(d=>d?{value:d,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new Fe(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function ip(r,l){const{id:i,choices:s,created:f,model:d,system_fingerprint:m,...b}=r,A={...b,id:i,choices:s.map(({message:v,finish_reason:R,index:M,logprobs:U,...C})=>{if(!R)throw new nt(`missing finish_reason for choice ${M}`);const{content:q=null,function_call:Q,tool_calls:Z,...Et}=v,$=v.role;if(!$)throw new nt(`missing role for choice ${M}`);if(Q){const{arguments:J,name:V}=Q;if(J==null)throw new nt(`missing function_call.arguments for choice ${M}`);if(!V)throw new nt(`missing function_call.name for choice ${M}`);return{...C,message:{content:q,function_call:{arguments:J,name:V},role:$,refusal:v.refusal??null},finish_reason:R,index:M,logprobs:U}}return Z?{...C,index:M,finish_reason:R,logprobs:U,message:{...Et,role:$,content:q,refusal:v.refusal??null,tool_calls:Z.map((J,V)=>{const{function:k,type:ct,id:tt,...Rt}=J,{arguments:mt,name:bt,...ot}=k||{};if(tt==null)throw new nt(`missing choices[${M}].tool_calls[${V}].id
${Xi(r)}`);if(ct==null)throw new nt(`missing choices[${M}].tool_calls[${V}].type
${Xi(r)}`);if(bt==null)throw new nt(`missing choices[${M}].tool_calls[${V}].function.name
${Xi(r)}`);if(mt==null)throw new nt(`missing choices[${M}].tool_calls[${V}].function.arguments
${Xi(r)}`);return{...Rt,id:tt,type:ct,function:{...ot,name:bt,arguments:mt}}})}}:{...C,message:{...Et,content:q,role:$,refusal:v.refusal??null},finish_reason:R,index:M,logprobs:U}}),created:f,model:d,object:"chat.completion",...m?{system_fingerprint:m}:{}};return kv(A,l)}function Xi(r){return JSON.stringify(r)}class ls extends _u{static fromReadableStream(l){const i=new ls(null);return i._run(()=>i._fromReadableStream(l)),i}static runTools(l,i,s){const f=new ls(i),d={...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return f._run(()=>f._runTools(l,i,d)),f}}let of=class extends ut{constructor(){super(...arguments),this.messages=new Ry(this._client)}create(l,i){return this._client.post("/chat/completions",{body:l,...i,stream:l.stream??!1})}retrieve(l,i){return this._client.get(H`/chat/completions/${l}`,i)}update(l,i,s){return this._client.post(H`/chat/completions/${l}`,{body:i,...s})}list(l={},i){return this._client.getAPIList("/chat/completions",Lt,{query:l,...i})}delete(l,i){return this._client.delete(H`/chat/completions/${l}`,i)}parse(l,i){return Pv(l.tools),this._client.chat.completions.create(l,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(s=>rf(s,l))}runTools(l,i){return l.stream?ls.runTools(this._client,l,i):ff.runTools(this._client,l,i)}stream(l,i){return _u.createChatCompletion(this._client,l,i)}};of.Messages=Ry;class df extends ut{constructor(){super(...arguments),this.completions=new of(this._client)}}df.Completions=of;const Ky=Symbol("brand.privateNullableHeaders");function*sp(r){if(!r)return;if(Ky in r){const{values:s,nulls:f}=r;yield*s.entries();for(const d of f)yield[d,null];return}let l=!1,i;r instanceof Headers?i=r.entries():Mm(r)?i=r:(l=!0,i=Object.entries(r??{}));for(let s of i){const f=s[0];if(typeof f!="string")throw new TypeError("expected header name to be a string");const d=Mm(s[1])?s[1]:[s[1]];let m=!1;for(const b of d)b!==void 0&&(l&&!m&&(m=!0,yield[f,null]),yield[f,b])}}const W=r=>{const l=new Headers,i=new Set;for(const s of r){const f=new Set;for(const[d,m]of sp(s)){const b=d.toLowerCase();f.has(b)||(l.delete(d),f.add(b)),m===null?(l.delete(d),i.add(b)):(l.append(d,m),i.delete(b))}}return{[Ky]:!0,values:l,nulls:i}};class Jy extends ut{create(l,i){return this._client.post("/audio/speech",{body:l,...i,headers:W([{Accept:"application/octet-stream"},i==null?void 0:i.headers]),__binaryResponse:!0})}}class ky extends ut{create(l,i){return this._client.post("/audio/transcriptions",mn({body:l,...i,stream:l.stream??!1,__metadata:{model:l.model}},this._client))}}class Wy extends ut{create(l,i){return this._client.post("/audio/translations",mn({body:l,...i,__metadata:{model:l.model}},this._client))}}class bu extends ut{constructor(){super(...arguments),this.transcriptions=new ky(this._client),this.translations=new Wy(this._client),this.speech=new Jy(this._client)}}bu.Transcriptions=ky;bu.Translations=Wy;bu.Speech=Jy;class Fy extends ut{create(l,i){return this._client.post("/batches",{body:l,...i})}retrieve(l,i){return this._client.get(H`/batches/${l}`,i)}list(l={},i){return this._client.getAPIList("/batches",Lt,{query:l,...i})}cancel(l,i){return this._client.post(H`/batches/${l}/cancel`,i)}}class Iy extends ut{create(l,i){return this._client.post("/assistants",{body:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(l,i){return this._client.get(H`/assistants/${l}`,{...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(l,i,s){return this._client.post(H`/assistants/${l}`,{body:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(l={},i){return this._client.getAPIList("/assistants",Lt,{query:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(l,i){return this._client.delete(H`/assistants/${l}`,{...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class Py extends ut{create(l,i){return this._client.post("/realtime/sessions",{body:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class tg extends ut{create(l,i){return this._client.post("/realtime/transcription_sessions",{body:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class fs extends ut{constructor(){super(...arguments),this.sessions=new Py(this._client),this.transcriptionSessions=new tg(this._client)}}fs.Sessions=Py;fs.TranscriptionSessions=tg;class eg extends ut{create(l,i,s){return this._client.post(H`/threads/${l}/messages`,{body:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(l,i,s){const{thread_id:f}=i;return this._client.get(H`/threads/${f}/messages/${l}`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(l,i,s){const{thread_id:f,...d}=i;return this._client.post(H`/threads/${f}/messages/${l}`,{body:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(l,i={},s){return this._client.getAPIList(H`/threads/${l}/messages`,Lt,{query:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(l,i,s){const{thread_id:f}=i;return this._client.delete(H`/threads/${f}/messages/${l}`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}class ag extends ut{retrieve(l,i,s){const{thread_id:f,run_id:d,...m}=i;return this._client.get(H`/threads/${f}/runs/${d}/steps/${l}`,{query:m,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(l,i,s){const{thread_id:f,...d}=i;return this._client.getAPIList(H`/threads/${f}/runs/${l}/steps`,Lt,{query:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}const cp=r=>{if(typeof Buffer<"u"){const l=Buffer.from(r,"base64");return Array.from(new Float32Array(l.buffer,l.byteOffset,l.length/Float32Array.BYTES_PER_ELEMENT))}else{const l=atob(r),i=l.length,s=new Uint8Array(i);for(let f=0;f<i;f++)s[f]=l.charCodeAt(f);return Array.from(new Float32Array(s.buffer))}};var Gr={};const uu=r=>{var l,i,s,f;if(typeof globalThis.process<"u")return((l=Gr==null?void 0:Gr[r])==null?void 0:l.trim())??void 0;if(typeof globalThis.Deno<"u")return(f=(s=(i=globalThis.Deno.env)==null?void 0:i.get)==null?void 0:s.call(i,r))==null?void 0:f.trim()};var ee,on,tf,We,Ji,Ge,dn,ll,fn,us,Re,ki,Wi,yu,du,hu,Zm,Vm,$m,Km,Jm,km,Wm;class gu extends sf{constructor(){super(...arguments),ee.add(this),tf.set(this,[]),We.set(this,{}),Ji.set(this,{}),Ge.set(this,void 0),dn.set(this,void 0),ll.set(this,void 0),fn.set(this,void 0),us.set(this,void 0),Re.set(this,void 0),ki.set(this,void 0),Wi.set(this,void 0),yu.set(this,void 0)}[(tf=new WeakMap,We=new WeakMap,Ji=new WeakMap,Ge=new WeakMap,dn=new WeakMap,ll=new WeakMap,fn=new WeakMap,us=new WeakMap,Re=new WeakMap,ki=new WeakMap,Wi=new WeakMap,yu=new WeakMap,ee=new WeakSet,Symbol.asyncIterator)](){const l=[],i=[];let s=!1;return this.on("event",f=>{const d=i.shift();d?d.resolve(f):l.push(f)}),this.on("end",()=>{s=!0;for(const f of i)f.resolve(void 0);i.length=0}),this.on("abort",f=>{s=!0;for(const d of i)d.reject(f);i.length=0}),this.on("error",f=>{s=!0;for(const d of i)d.reject(f);i.length=0}),{next:async()=>l.length?{value:l.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((d,m)=>i.push({resolve:d,reject:m})).then(d=>d?{value:d,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(l){const i=new on;return i._run(()=>i._fromReadableStream(l)),i}async _fromReadableStream(l,i){var d;const s=i==null?void 0:i.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();const f=Fe.fromReadableStream(l,this.controller);for await(const m of f)O(this,ee,"m",du).call(this,m);if((d=f.controller.signal)!=null&&d.aborted)throw new qe;return this._addRun(O(this,ee,"m",hu).call(this))}toReadableStream(){return new Fe(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(l,i,s,f){const d=new on;return d._run(()=>d._runToolAssistantStream(l,i,s,{...f,headers:{...f==null?void 0:f.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createToolAssistantStream(l,i,s,f){var A;const d=f==null?void 0:f.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));const m={...s,stream:!0},b=await l.submitToolOutputs(i,m,{...f,signal:this.controller.signal});this._connected();for await(const v of b)O(this,ee,"m",du).call(this,v);if((A=b.controller.signal)!=null&&A.aborted)throw new qe;return this._addRun(O(this,ee,"m",hu).call(this))}static createThreadAssistantStream(l,i,s){const f=new on;return f._run(()=>f._threadAssistantStream(l,i,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),f}static createAssistantStream(l,i,s,f){const d=new on;return d._run(()=>d._runAssistantStream(l,i,s,{...f,headers:{...f==null?void 0:f.headers,"X-Stainless-Helper-Method":"stream"}})),d}currentEvent(){return O(this,ki,"f")}currentRun(){return O(this,Wi,"f")}currentMessageSnapshot(){return O(this,Ge,"f")}currentRunStepSnapshot(){return O(this,yu,"f")}async finalRunSteps(){return await this.done(),Object.values(O(this,We,"f"))}async finalMessages(){return await this.done(),Object.values(O(this,Ji,"f"))}async finalRun(){if(await this.done(),!O(this,dn,"f"))throw Error("Final run was not received.");return O(this,dn,"f")}async _createThreadAssistantStream(l,i,s){var b;const f=s==null?void 0:s.signal;f&&(f.aborted&&this.controller.abort(),f.addEventListener("abort",()=>this.controller.abort()));const d={...i,stream:!0},m=await l.createAndRun(d,{...s,signal:this.controller.signal});this._connected();for await(const A of m)O(this,ee,"m",du).call(this,A);if((b=m.controller.signal)!=null&&b.aborted)throw new qe;return this._addRun(O(this,ee,"m",hu).call(this))}async _createAssistantStream(l,i,s,f){var A;const d=f==null?void 0:f.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));const m={...s,stream:!0},b=await l.create(i,m,{...f,signal:this.controller.signal});this._connected();for await(const v of b)O(this,ee,"m",du).call(this,v);if((A=b.controller.signal)!=null&&A.aborted)throw new qe;return this._addRun(O(this,ee,"m",hu).call(this))}static accumulateDelta(l,i){for(const[s,f]of Object.entries(i)){if(!l.hasOwnProperty(s)){l[s]=f;continue}let d=l[s];if(d==null){l[s]=f;continue}if(s==="index"||s==="type"){l[s]=f;continue}if(typeof d=="string"&&typeof f=="string")d+=f;else if(typeof d=="number"&&typeof f=="number")d+=f;else if(Ur(d)&&Ur(f))d=this.accumulateDelta(d,f);else if(Array.isArray(d)&&Array.isArray(f)){if(d.every(m=>typeof m=="string"||typeof m=="number")){d.push(...f);continue}for(const m of f){if(!Ur(m))throw new Error(`Expected array delta entry to be an object but got: ${m}`);const b=m.index;if(b==null)throw console.error(m),new Error("Expected array delta entry to have an `index` property");if(typeof b!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${b}`);const A=d[b];A==null?d.push(m):d[b]=this.accumulateDelta(A,m)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${f}, accValue: ${d}`);l[s]=d}return l}_addRun(l){return l}async _threadAssistantStream(l,i,s){return await this._createThreadAssistantStream(i,l,s)}async _runAssistantStream(l,i,s,f){return await this._createAssistantStream(i,l,s,f)}async _runToolAssistantStream(l,i,s,f){return await this._createToolAssistantStream(i,l,s,f)}}on=gu,du=function(l){if(!this.ended)switch(lt(this,ki,l),O(this,ee,"m",$m).call(this,l),l.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":O(this,ee,"m",Wm).call(this,l);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":O(this,ee,"m",Vm).call(this,l);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":O(this,ee,"m",Zm).call(this,l);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},hu=function(){if(this.ended)throw new nt("stream has ended, this shouldn't happen");if(!O(this,dn,"f"))throw Error("Final run has not been received");return O(this,dn,"f")},Zm=function(l){const[i,s]=O(this,ee,"m",Jm).call(this,l,O(this,Ge,"f"));lt(this,Ge,i),O(this,Ji,"f")[i.id]=i;for(const f of s){const d=i.content[f.index];(d==null?void 0:d.type)=="text"&&this._emit("textCreated",d.text)}switch(l.event){case"thread.message.created":this._emit("messageCreated",l.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",l.data.delta,i),l.data.delta.content)for(const f of l.data.delta.content){if(f.type=="text"&&f.text){let d=f.text,m=i.content[f.index];if(m&&m.type=="text")this._emit("textDelta",d,m.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(f.index!=O(this,ll,"f")){if(O(this,fn,"f"))switch(O(this,fn,"f").type){case"text":this._emit("textDone",O(this,fn,"f").text,O(this,Ge,"f"));break;case"image_file":this._emit("imageFileDone",O(this,fn,"f").image_file,O(this,Ge,"f"));break}lt(this,ll,f.index)}lt(this,fn,i.content[f.index])}break;case"thread.message.completed":case"thread.message.incomplete":if(O(this,ll,"f")!==void 0){const f=l.data.content[O(this,ll,"f")];if(f)switch(f.type){case"image_file":this._emit("imageFileDone",f.image_file,O(this,Ge,"f"));break;case"text":this._emit("textDone",f.text,O(this,Ge,"f"));break}}O(this,Ge,"f")&&this._emit("messageDone",l.data),lt(this,Ge,void 0)}},Vm=function(l){const i=O(this,ee,"m",Km).call(this,l);switch(lt(this,yu,i),l.event){case"thread.run.step.created":this._emit("runStepCreated",l.data);break;case"thread.run.step.delta":const s=l.data.delta;if(s.step_details&&s.step_details.type=="tool_calls"&&s.step_details.tool_calls&&i.step_details.type=="tool_calls")for(const d of s.step_details.tool_calls)d.index==O(this,us,"f")?this._emit("toolCallDelta",d,i.step_details.tool_calls[d.index]):(O(this,Re,"f")&&this._emit("toolCallDone",O(this,Re,"f")),lt(this,us,d.index),lt(this,Re,i.step_details.tool_calls[d.index]),O(this,Re,"f")&&this._emit("toolCallCreated",O(this,Re,"f")));this._emit("runStepDelta",l.data.delta,i);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":lt(this,yu,void 0),l.data.step_details.type=="tool_calls"&&O(this,Re,"f")&&(this._emit("toolCallDone",O(this,Re,"f")),lt(this,Re,void 0)),this._emit("runStepDone",l.data,i);break}},$m=function(l){O(this,tf,"f").push(l),this._emit("event",l)},Km=function(l){switch(l.event){case"thread.run.step.created":return O(this,We,"f")[l.data.id]=l.data,l.data;case"thread.run.step.delta":let i=O(this,We,"f")[l.data.id];if(!i)throw Error("Received a RunStepDelta before creation of a snapshot");let s=l.data;if(s.delta){const f=on.accumulateDelta(i,s.delta);O(this,We,"f")[l.data.id]=f}return O(this,We,"f")[l.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":O(this,We,"f")[l.data.id]=l.data;break}if(O(this,We,"f")[l.data.id])return O(this,We,"f")[l.data.id];throw new Error("No snapshot available")},Jm=function(l,i){let s=[];switch(l.event){case"thread.message.created":return[l.data,s];case"thread.message.delta":if(!i)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let f=l.data;if(f.delta.content)for(const d of f.delta.content)if(d.index in i.content){let m=i.content[d.index];i.content[d.index]=O(this,ee,"m",km).call(this,d,m)}else i.content[d.index]=d,s.push(d);return[i,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(i)return[i,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},km=function(l,i){return on.accumulateDelta(i,l)},Wm=function(l){switch(lt(this,Wi,l.data),l.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":lt(this,dn,l.data),O(this,Re,"f")&&(this._emit("toolCallDone",O(this,Re,"f")),lt(this,Re,void 0));break}};let hf=class extends ut{constructor(){super(...arguments),this.steps=new ag(this._client)}create(l,i,s){const{include:f,...d}=i;return this._client.post(H`/threads/${l}/runs`,{query:{include:f},body:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers]),stream:i.stream??!1})}retrieve(l,i,s){const{thread_id:f}=i;return this._client.get(H`/threads/${f}/runs/${l}`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(l,i,s){const{thread_id:f,...d}=i;return this._client.post(H`/threads/${f}/runs/${l}`,{body:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(l,i={},s){return this._client.getAPIList(H`/threads/${l}/runs`,Lt,{query:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}cancel(l,i,s){const{thread_id:f}=i;return this._client.post(H`/threads/${f}/runs/${l}/cancel`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(l,i,s){const f=await this.create(l,i,s);return await this.poll(f.id,{thread_id:l},s)}createAndStream(l,i,s){return gu.createAssistantStream(l,this._client.beta.threads.runs,i,s)}async poll(l,i,s){var d;const f=W([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((d=s==null?void 0:s.pollIntervalMs)==null?void 0:d.toString())??void 0}]);for(;;){const{data:m,response:b}=await this.retrieve(l,i,{...s,headers:{...s==null?void 0:s.headers,...f}}).withResponse();switch(m.status){case"queued":case"in_progress":case"cancelling":let A=5e3;if(s!=null&&s.pollIntervalMs)A=s.pollIntervalMs;else{const v=b.headers.get("openai-poll-after-ms");if(v){const R=parseInt(v);isNaN(R)||(A=R)}}await vu(A);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return m}}}stream(l,i,s){return gu.createAssistantStream(l,this._client.beta.threads.runs,i,s)}submitToolOutputs(l,i,s){const{thread_id:f,...d}=i;return this._client.post(H`/threads/${f}/runs/${l}/submit_tool_outputs`,{body:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers]),stream:i.stream??!1})}async submitToolOutputsAndPoll(l,i,s){const f=await this.submitToolOutputs(l,i,s);return await this.poll(f.id,i,s)}submitToolOutputsStream(l,i,s){return gu.createToolAssistantStream(l,this._client.beta.threads.runs,i,s)}};hf.Steps=ag;class os extends ut{constructor(){super(...arguments),this.runs=new hf(this._client),this.messages=new eg(this._client)}create(l={},i){return this._client.post("/threads",{body:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(l,i){return this._client.get(H`/threads/${l}`,{...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(l,i,s){return this._client.post(H`/threads/${l}`,{body:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(l,i){return this._client.delete(H`/threads/${l}`,{...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}createAndRun(l,i){return this._client.post("/threads/runs",{body:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers]),stream:l.stream??!1})}async createAndRunPoll(l,i){const s=await this.createAndRun(l,i);return await this.runs.poll(s.id,{thread_id:s.thread_id},i)}createAndRunStream(l,i){return gu.createThreadAssistantStream(l,this._client.beta.threads,i)}}os.Runs=hf;os.Messages=eg;class Su extends ut{constructor(){super(...arguments),this.realtime=new fs(this._client),this.assistants=new Iy(this._client),this.threads=new os(this._client)}}Su.Realtime=fs;Su.Assistants=Iy;Su.Threads=os;class ng extends ut{create(l,i){return this._client.post("/completions",{body:l,...i,stream:l.stream??!1})}}class lg extends ut{retrieve(l,i,s){const{container_id:f}=i;return this._client.get(H`/containers/${f}/files/${l}/content`,{...s,headers:W([{Accept:"application/binary"},s==null?void 0:s.headers]),__binaryResponse:!0})}}let mf=class extends ut{constructor(){super(...arguments),this.content=new lg(this._client)}create(l,i,s){return this._client.post(H`/containers/${l}/files`,mn({body:i,...s},this._client))}retrieve(l,i,s){const{container_id:f}=i;return this._client.get(H`/containers/${f}/files/${l}`,s)}list(l,i={},s){return this._client.getAPIList(H`/containers/${l}/files`,Lt,{query:i,...s})}delete(l,i,s){const{container_id:f}=i;return this._client.delete(H`/containers/${f}/files/${l}`,{...s,headers:W([{Accept:"*/*"},s==null?void 0:s.headers])})}};mf.Content=lg;class yf extends ut{constructor(){super(...arguments),this.files=new mf(this._client)}create(l,i){return this._client.post("/containers",{body:l,...i})}retrieve(l,i){return this._client.get(H`/containers/${l}`,i)}list(l={},i){return this._client.getAPIList("/containers",Lt,{query:l,...i})}delete(l,i){return this._client.delete(H`/containers/${l}`,{...i,headers:W([{Accept:"*/*"},i==null?void 0:i.headers])})}}yf.Files=mf;class ug extends ut{create(l,i){const s=!!l.encoding_format;let f=s?l.encoding_format:"base64";s&&ie(this._client).debug("embeddings/user defined encoding_format:",l.encoding_format);const d=this._client.post("/embeddings",{body:{...l,encoding_format:f},...i});return s?d:(ie(this._client).debug("embeddings/decoding base64 embeddings from base64"),d._thenUnwrap(m=>(m&&m.data&&m.data.forEach(b=>{const A=b.embedding;b.embedding=cp(A)}),m)))}}class ig extends ut{retrieve(l,i,s){const{eval_id:f,run_id:d}=i;return this._client.get(H`/evals/${f}/runs/${d}/output_items/${l}`,s)}list(l,i,s){const{eval_id:f,...d}=i;return this._client.getAPIList(H`/evals/${f}/runs/${l}/output_items`,Lt,{query:d,...s})}}class gf extends ut{constructor(){super(...arguments),this.outputItems=new ig(this._client)}create(l,i,s){return this._client.post(H`/evals/${l}/runs`,{body:i,...s})}retrieve(l,i,s){const{eval_id:f}=i;return this._client.get(H`/evals/${f}/runs/${l}`,s)}list(l,i={},s){return this._client.getAPIList(H`/evals/${l}/runs`,Lt,{query:i,...s})}delete(l,i,s){const{eval_id:f}=i;return this._client.delete(H`/evals/${f}/runs/${l}`,s)}cancel(l,i,s){const{eval_id:f}=i;return this._client.post(H`/evals/${f}/runs/${l}`,s)}}gf.OutputItems=ig;class _f extends ut{constructor(){super(...arguments),this.runs=new gf(this._client)}create(l,i){return this._client.post("/evals",{body:l,...i})}retrieve(l,i){return this._client.get(H`/evals/${l}`,i)}update(l,i,s){return this._client.post(H`/evals/${l}`,{body:i,...s})}list(l={},i){return this._client.getAPIList("/evals",Lt,{query:l,...i})}delete(l,i){return this._client.delete(H`/evals/${l}`,i)}}_f.Runs=gf;let sg=class extends ut{create(l,i){return this._client.post("/files",mn({body:l,...i},this._client))}retrieve(l,i){return this._client.get(H`/files/${l}`,i)}list(l={},i){return this._client.getAPIList("/files",Lt,{query:l,...i})}delete(l,i){return this._client.delete(H`/files/${l}`,i)}content(l,i){return this._client.get(H`/files/${l}/content`,{...i,headers:W([{Accept:"application/binary"},i==null?void 0:i.headers]),__binaryResponse:!0})}async waitForProcessing(l,{pollInterval:i=5e3,maxWait:s=30*60*1e3}={}){const f=new Set(["processed","error","deleted"]),d=Date.now();let m=await this.retrieve(l);for(;!m.status||!f.has(m.status);)if(await vu(i),m=await this.retrieve(l),Date.now()-d>s)throw new lf({message:`Giving up on waiting for file ${l} to finish processing after ${s} milliseconds.`});return m}};class cg extends ut{}let rg=class extends ut{run(l,i){return this._client.post("/fine_tuning/alpha/graders/run",{body:l,...i})}validate(l,i){return this._client.post("/fine_tuning/alpha/graders/validate",{body:l,...i})}};class vf extends ut{constructor(){super(...arguments),this.graders=new rg(this._client)}}vf.Graders=rg;class fg extends ut{create(l,i,s){return this._client.getAPIList(H`/fine_tuning/checkpoints/${l}/permissions`,rs,{body:i,method:"post",...s})}retrieve(l,i={},s){return this._client.getAPIList(H`/fine_tuning/checkpoints/${l}/permissions`,Lt,{query:i,...s})}delete(l,i,s){const{fine_tuned_model_checkpoint:f}=i;return this._client.delete(H`/fine_tuning/checkpoints/${f}/permissions/${l}`,s)}}let pf=class extends ut{constructor(){super(...arguments),this.permissions=new fg(this._client)}};pf.Permissions=fg;class og extends ut{list(l,i={},s){return this._client.getAPIList(H`/fine_tuning/jobs/${l}/checkpoints`,Lt,{query:i,...s})}}class bf extends ut{constructor(){super(...arguments),this.checkpoints=new og(this._client)}create(l,i){return this._client.post("/fine_tuning/jobs",{body:l,...i})}retrieve(l,i){return this._client.get(H`/fine_tuning/jobs/${l}`,i)}list(l={},i){return this._client.getAPIList("/fine_tuning/jobs",Lt,{query:l,...i})}cancel(l,i){return this._client.post(H`/fine_tuning/jobs/${l}/cancel`,i)}listEvents(l,i={},s){return this._client.getAPIList(H`/fine_tuning/jobs/${l}/events`,Lt,{query:i,...s})}pause(l,i){return this._client.post(H`/fine_tuning/jobs/${l}/pause`,i)}resume(l,i){return this._client.post(H`/fine_tuning/jobs/${l}/resume`,i)}}bf.Checkpoints=og;class ul extends ut{constructor(){super(...arguments),this.methods=new cg(this._client),this.jobs=new bf(this._client),this.checkpoints=new pf(this._client),this.alpha=new vf(this._client)}}ul.Methods=cg;ul.Jobs=bf;ul.Checkpoints=pf;ul.Alpha=vf;class dg extends ut{}class Sf extends ut{constructor(){super(...arguments),this.graderModels=new dg(this._client)}}Sf.GraderModels=dg;class hg extends ut{createVariation(l,i){return this._client.post("/images/variations",mn({body:l,...i},this._client))}edit(l,i){return this._client.post("/images/edits",mn({body:l,...i},this._client))}generate(l,i){return this._client.post("/images/generations",{body:l,...i})}}class mg extends ut{retrieve(l,i){return this._client.get(H`/models/${l}`,i)}list(l){return this._client.getAPIList("/models",rs,l)}delete(l,i){return this._client.delete(H`/models/${l}`,i)}}class yg extends ut{create(l,i){return this._client.post("/moderations",{body:l,...i})}}function rp(r,l){return!l||!op(l)?{...r,output_parsed:null,output:r.output.map(i=>i.type==="function_call"?{...i,parsed_arguments:null}:i.type==="message"?{...i,content:i.content.map(s=>({...s,parsed:null}))}:i)}:gg(r,l)}function gg(r,l){const i=r.output.map(f=>{if(f.type==="function_call")return{...f,parsed_arguments:mp(l,f)};if(f.type==="message"){const d=f.content.map(m=>m.type==="output_text"?{...m,parsed:fp(l,m.text)}:m);return{...f,content:d}}return f}),s=Object.assign({},r,{output:i});return Object.getOwnPropertyDescriptor(r,"output_text")||_g(s),Object.defineProperty(s,"output_parsed",{enumerable:!0,get(){for(const f of s.output)if(f.type==="message"){for(const d of f.content)if(d.type==="output_text"&&d.parsed!==null)return d.parsed}return null}}),s}function fp(r,l){var i,s,f,d;return((s=(i=r.text)==null?void 0:i.format)==null?void 0:s.type)!=="json_schema"?null:"$parseRaw"in((f=r.text)==null?void 0:f.format)?((d=r.text)==null?void 0:d.format).$parseRaw(l):JSON.parse(l)}function op(r){var l;return!!cf((l=r.text)==null?void 0:l.format)}function dp(r){return(r==null?void 0:r.$brand)==="auto-parseable-tool"}function hp(r,l){return r.find(i=>i.type==="function"&&i.name===l)}function mp(r,l){const i=hp(r.tools??[],l.name);return{...l,...l,parsed_arguments:dp(i)?i.$parseRaw(l.arguments):i!=null&&i.strict?JSON.parse(l.arguments):null}}function _g(r){const l=[];for(const i of r.output)if(i.type==="message")for(const s of i.content)s.type==="output_text"&&l.push(s.text);r.output_text=l.join("")}var el,Gi,Qa,Qi,Fm,Im,Pm,ty;class Af extends sf{constructor(l){super(),el.add(this),Gi.set(this,void 0),Qa.set(this,void 0),Qi.set(this,void 0),lt(this,Gi,l)}static createResponse(l,i,s){const f=new Af(i);return f._run(()=>f._createOrRetrieveResponse(l,i,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),f}async _createOrRetrieveResponse(l,i,s){var b;const f=s==null?void 0:s.signal;f&&(f.aborted&&this.controller.abort(),f.addEventListener("abort",()=>this.controller.abort())),O(this,el,"m",Fm).call(this);let d,m=null;"response_id"in i?(d=await l.responses.retrieve(i.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),m=i.starting_after??null):d=await l.responses.create({...i,stream:!0},{...s,signal:this.controller.signal}),this._connected();for await(const A of d)O(this,el,"m",Im).call(this,A,m);if((b=d.controller.signal)!=null&&b.aborted)throw new qe;return O(this,el,"m",Pm).call(this)}[(Gi=new WeakMap,Qa=new WeakMap,Qi=new WeakMap,el=new WeakSet,Fm=function(){this.ended||lt(this,Qa,void 0)},Im=function(i,s){if(this.ended)return;const f=(m,b)=>{(s==null||b.sequence_number>s)&&this._emit(m,b)},d=O(this,el,"m",ty).call(this,i);switch(f("event",i),i.type){case"response.output_text.delta":{const m=d.output[i.output_index];if(!m)throw new nt(`missing output at index ${i.output_index}`);if(m.type==="message"){const b=m.content[i.content_index];if(!b)throw new nt(`missing content at index ${i.content_index}`);if(b.type!=="output_text")throw new nt(`expected content to be 'output_text', got ${b.type}`);f("response.output_text.delta",{...i,snapshot:b.text})}break}case"response.function_call_arguments.delta":{const m=d.output[i.output_index];if(!m)throw new nt(`missing output at index ${i.output_index}`);m.type==="function_call"&&f("response.function_call_arguments.delta",{...i,snapshot:m.arguments});break}default:f(i.type,i);break}},Pm=function(){if(this.ended)throw new nt("stream has ended, this shouldn't happen");const i=O(this,Qa,"f");if(!i)throw new nt("request ended without sending any events");lt(this,Qa,void 0);const s=yp(i,O(this,Gi,"f"));return lt(this,Qi,s),s},ty=function(i){let s=O(this,Qa,"f");if(!s){if(i.type!=="response.created")throw new nt(`When snapshot hasn't been set yet, expected 'response.created' event, got ${i.type}`);return s=lt(this,Qa,i.response),s}switch(i.type){case"response.output_item.added":{s.output.push(i.item);break}case"response.content_part.added":{const f=s.output[i.output_index];if(!f)throw new nt(`missing output at index ${i.output_index}`);f.type==="message"&&f.content.push(i.part);break}case"response.output_text.delta":{const f=s.output[i.output_index];if(!f)throw new nt(`missing output at index ${i.output_index}`);if(f.type==="message"){const d=f.content[i.content_index];if(!d)throw new nt(`missing content at index ${i.content_index}`);if(d.type!=="output_text")throw new nt(`expected content to be 'output_text', got ${d.type}`);d.text+=i.delta}break}case"response.function_call_arguments.delta":{const f=s.output[i.output_index];if(!f)throw new nt(`missing output at index ${i.output_index}`);f.type==="function_call"&&(f.arguments+=i.delta);break}case"response.completed":{lt(this,Qa,i.response);break}}return s},Symbol.asyncIterator)](){const l=[],i=[];let s=!1;return this.on("event",f=>{const d=i.shift();d?d.resolve(f):l.push(f)}),this.on("end",()=>{s=!0;for(const f of i)f.resolve(void 0);i.length=0}),this.on("abort",f=>{s=!0;for(const d of i)d.reject(f);i.length=0}),this.on("error",f=>{s=!0;for(const d of i)d.reject(f);i.length=0}),{next:async()=>l.length?{value:l.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((d,m)=>i.push({resolve:d,reject:m})).then(d=>d?{value:d,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const l=O(this,Qi,"f");if(!l)throw new nt("stream ended without producing a ChatCompletion");return l}}function yp(r,l){return rp(r,l)}class vg extends ut{list(l,i={},s){return this._client.getAPIList(H`/responses/${l}/input_items`,Lt,{query:i,...s})}}class Ef extends ut{constructor(){super(...arguments),this.inputItems=new vg(this._client)}create(l,i){return this._client.post("/responses",{body:l,...i,stream:l.stream??!1})._thenUnwrap(s=>("object"in s&&s.object==="response"&&_g(s),s))}retrieve(l,i={},s){return this._client.get(H`/responses/${l}`,{query:i,...s,stream:(i==null?void 0:i.stream)??!1})}delete(l,i){return this._client.delete(H`/responses/${l}`,{...i,headers:W([{Accept:"*/*"},i==null?void 0:i.headers])})}parse(l,i){return this._client.responses.create(l,i)._thenUnwrap(s=>gg(s,l))}stream(l,i){return Af.createResponse(this._client,l,i)}cancel(l,i){return this._client.post(H`/responses/${l}/cancel`,i)}}Ef.InputItems=vg;class pg extends ut{create(l,i,s){return this._client.post(H`/uploads/${l}/parts`,mn({body:i,...s},this._client))}}class wf extends ut{constructor(){super(...arguments),this.parts=new pg(this._client)}create(l,i){return this._client.post("/uploads",{body:l,...i})}cancel(l,i){return this._client.post(H`/uploads/${l}/cancel`,i)}complete(l,i,s){return this._client.post(H`/uploads/${l}/complete`,{body:i,...s})}}wf.Parts=pg;const gp=async r=>{const l=await Promise.allSettled(r),i=l.filter(f=>f.status==="rejected");if(i.length){for(const f of i)console.error(f.reason);throw new Error(`${i.length} promise(s) failed - see the above errors`)}const s=[];for(const f of l)f.status==="fulfilled"&&s.push(f.value);return s};class bg extends ut{create(l,i,s){return this._client.post(H`/vector_stores/${l}/file_batches`,{body:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(l,i,s){const{vector_store_id:f}=i;return this._client.get(H`/vector_stores/${f}/file_batches/${l}`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}cancel(l,i,s){const{vector_store_id:f}=i;return this._client.post(H`/vector_stores/${f}/file_batches/${l}/cancel`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(l,i,s){const f=await this.create(l,i);return await this.poll(l,f.id,s)}listFiles(l,i,s){const{vector_store_id:f,...d}=i;return this._client.getAPIList(H`/vector_stores/${f}/file_batches/${l}/files`,Lt,{query:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async poll(l,i,s){var d;const f=W([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((d=s==null?void 0:s.pollIntervalMs)==null?void 0:d.toString())??void 0}]);for(;;){const{data:m,response:b}=await this.retrieve(i,{vector_store_id:l},{...s,headers:f}).withResponse();switch(m.status){case"in_progress":let A=5e3;if(s!=null&&s.pollIntervalMs)A=s.pollIntervalMs;else{const v=b.headers.get("openai-poll-after-ms");if(v){const R=parseInt(v);isNaN(R)||(A=R)}}await vu(A);break;case"failed":case"cancelled":case"completed":return m}}}async uploadAndPoll(l,{files:i,fileIds:s=[]},f){if(i==null||i.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const d=(f==null?void 0:f.maxConcurrency)??5,m=Math.min(d,i.length),b=this._client,A=i.values(),v=[...s];async function R(U){for(let C of U){const q=await b.files.create({file:C,purpose:"assistants"},f);v.push(q.id)}}const M=Array(m).fill(A).map(R);return await gp(M),await this.createAndPoll(l,{file_ids:v})}}class Sg extends ut{create(l,i,s){return this._client.post(H`/vector_stores/${l}/files`,{body:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(l,i,s){const{vector_store_id:f}=i;return this._client.get(H`/vector_stores/${f}/files/${l}`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(l,i,s){const{vector_store_id:f,...d}=i;return this._client.post(H`/vector_stores/${f}/files/${l}`,{body:d,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(l,i={},s){return this._client.getAPIList(H`/vector_stores/${l}/files`,Lt,{query:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(l,i,s){const{vector_store_id:f}=i;return this._client.delete(H`/vector_stores/${f}/files/${l}`,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(l,i,s){const f=await this.create(l,i,s);return await this.poll(l,f.id,s)}async poll(l,i,s){var d;const f=W([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((d=s==null?void 0:s.pollIntervalMs)==null?void 0:d.toString())??void 0}]);for(;;){const m=await this.retrieve(i,{vector_store_id:l},{...s,headers:f}).withResponse(),b=m.data;switch(b.status){case"in_progress":let A=5e3;if(s!=null&&s.pollIntervalMs)A=s.pollIntervalMs;else{const v=m.response.headers.get("openai-poll-after-ms");if(v){const R=parseInt(v);isNaN(R)||(A=R)}}await vu(A);break;case"failed":case"completed":return b}}}async upload(l,i,s){const f=await this._client.files.create({file:i,purpose:"assistants"},s);return this.create(l,{file_id:f.id},s)}async uploadAndPoll(l,i,s){const f=await this.upload(l,i,s);return await this.poll(l,f.id,s)}content(l,i,s){const{vector_store_id:f}=i;return this._client.getAPIList(H`/vector_stores/${f}/files/${l}/content`,rs,{...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}class ds extends ut{constructor(){super(...arguments),this.files=new Sg(this._client),this.fileBatches=new bg(this._client)}create(l,i){return this._client.post("/vector_stores",{body:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(l,i){return this._client.get(H`/vector_stores/${l}`,{...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(l,i,s){return this._client.post(H`/vector_stores/${l}`,{body:i,...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(l={},i){return this._client.getAPIList("/vector_stores",Lt,{query:l,...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(l,i){return this._client.delete(H`/vector_stores/${l}`,{...i,headers:W([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}search(l,i,s){return this._client.getAPIList(H`/vector_stores/${l}/search`,rs,{body:i,method:"post",...s,headers:W([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}ds.Files=Sg;ds.FileBatches=bg;var ef,Tf,Fi,Ag;class gt{constructor({baseURL:l=uu("OPENAI_BASE_URL"),apiKey:i=uu("OPENAI_API_KEY"),organization:s=uu("OPENAI_ORG_ID")??null,project:f=uu("OPENAI_PROJECT_ID")??null,...d}={}){if(ef.add(this),Fi.set(this,void 0),this.completions=new ng(this),this.chat=new df(this),this.embeddings=new ug(this),this.files=new sg(this),this.images=new hg(this),this.audio=new bu(this),this.moderations=new yg(this),this.models=new mg(this),this.fineTuning=new ul(this),this.graders=new Sf(this),this.vectorStores=new ds(this),this.beta=new Su(this),this.batches=new Fy(this),this.uploads=new wf(this),this.responses=new Ef(this),this.evals=new _f(this),this.containers=new yf(this),i===void 0)throw new nt("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const m={apiKey:i,organization:s,project:f,...d,baseURL:l||"https://api.openai.com/v1"};if(!m.dangerouslyAllowBrowser&&_v())throw new nt(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);this.baseURL=m.baseURL,this.timeout=m.timeout??Tf.DEFAULT_TIMEOUT,this.logger=m.logger??console;const b="warn";this.logLevel=b,this.logLevel=Lm(m.logLevel,"ClientOptions.logLevel",this)??Lm(uu("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??b,this.fetchOptions=m.fetchOptions,this.maxRetries=m.maxRetries??2,this.fetch=m.fetch??Av(),lt(this,Fi,wv),this._options=m,this.apiKey=i,this.organization=s,this.project=f}withOptions(l){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...l})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:l,nulls:i}){}authHeaders(l){return W([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(l){return Nv(l,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${al}`}defaultIdempotencyKey(){return`stainless-node-retry-${ey()}`}makeStatusError(l,i,s,f){return ae.generate(l,i,s,f)}buildURL(l,i,s){const f=!O(this,ef,"m",Ag).call(this)&&s||this.baseURL,d=ov(l)?new URL(l):new URL(f+(f.endsWith("/")&&l.startsWith("/")?l.slice(1):l)),m=this.defaultQuery();return hv(m)||(i={...m,...i}),typeof i=="object"&&i&&!Array.isArray(i)&&(d.search=this.stringifyQuery(i)),d.toString()}async prepareOptions(l){}async prepareRequest(l,{url:i,options:s}){}get(l,i){return this.methodRequest("get",l,i)}post(l,i){return this.methodRequest("post",l,i)}patch(l,i){return this.methodRequest("patch",l,i)}put(l,i){return this.methodRequest("put",l,i)}delete(l,i){return this.methodRequest("delete",l,i)}methodRequest(l,i,s){return this.request(Promise.resolve(s).then(f=>({method:l,path:i,...f})))}request(l,i=null){return new cs(this,this.makeRequest(l,i,void 0))}async makeRequest(l,i,s){var Et,$;const f=await l,d=f.maxRetries??this.maxRetries;i==null&&(i=d),await this.prepareOptions(f);const{req:m,url:b,timeout:A}=this.buildRequest(f,{retryCount:d-i});await this.prepareRequest(m,{url:b,options:f});const v="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),R=s===void 0?"":`, retryOf: ${s}`,M=Date.now();if(ie(this).debug(`[${v}] sending request`,rn({retryOfRequestLogID:s,method:f.method,url:b,options:f,headers:m.headers})),(Et=f.signal)!=null&&Et.aborted)throw new qe;const U=new AbortController,C=await this.fetchWithTimeout(b,m,A,U).catch(Vr),q=Date.now();if(C instanceof Error){const J=`retrying, ${i} attempts remaining`;if(($=f.signal)!=null&&$.aborted)throw new qe;const V=Zr(C)||/timed? ?out/i.test(String(C)+("cause"in C?String(C.cause):""));if(i)return ie(this).info(`[${v}] connection ${V?"timed out":"failed"} - ${J}`),ie(this).debug(`[${v}] connection ${V?"timed out":"failed"} (${J})`,rn({retryOfRequestLogID:s,url:b,durationMs:q-M,message:C.message})),this.retryRequest(f,i,s??v);throw ie(this).info(`[${v}] connection ${V?"timed out":"failed"} - error; no more retries left`),ie(this).debug(`[${v}] connection ${V?"timed out":"failed"} (error; no more retries left)`,rn({retryOfRequestLogID:s,url:b,durationMs:q-M,message:C.message})),V?new lf:new is({cause:C})}const Q=[...C.headers.entries()].filter(([J])=>J==="x-request-id").map(([J,V])=>", "+J+": "+JSON.stringify(V)).join(""),Z=`[${v}${R}${Q}] ${m.method} ${b} ${C.ok?"succeeded":"failed"} with status ${C.status} in ${q-M}ms`;if(!C.ok){const J=this.shouldRetry(C);if(i&&J){const mt=`retrying, ${i} attempts remaining`;return await Ev(C.body),ie(this).info(`${Z} - ${mt}`),ie(this).debug(`[${v}] response error (${mt})`,rn({retryOfRequestLogID:s,url:C.url,status:C.status,headers:C.headers,durationMs:q-M})),this.retryRequest(f,i,s??v,C.headers)}const V=J?"error; no more retries left":"error; not retryable";ie(this).info(`${Z} - ${V}`);const k=await C.text().catch(mt=>Vr(mt).message),ct=gv(k),tt=ct?void 0:k;throw ie(this).debug(`[${v}] response error (${V})`,rn({retryOfRequestLogID:s,url:C.url,status:C.status,headers:C.headers,message:tt,durationMs:Date.now()-M})),this.makeStatusError(C.status,ct,tt,C.headers)}return ie(this).info(Z),ie(this).debug(`[${v}] response start`,rn({retryOfRequestLogID:s,url:C.url,status:C.status,headers:C.headers,durationMs:q-M})),{response:C,options:f,controller:U,requestLogID:v,retryOfRequestLogID:s,startTime:M}}getAPIList(l,i,s){return this.requestAPIList(i,{method:"get",path:l,...s})}requestAPIList(l,i){const s=this.makeRequest(i,null,void 0);return new Lv(this,s,l)}async fetchWithTimeout(l,i,s,f){const{signal:d,method:m,...b}=i||{};d&&d.addEventListener("abort",()=>f.abort());const A=setTimeout(()=>f.abort(),s),v=globalThis.ReadableStream&&b.body instanceof globalThis.ReadableStream||typeof b.body=="object"&&b.body!==null&&Symbol.asyncIterator in b.body,R={signal:f.signal,...v?{duplex:"half"}:{},method:"GET",...b};m&&(R.method=m.toUpperCase());try{return await this.fetch.call(void 0,l,R)}finally{clearTimeout(A)}}shouldRetry(l){const i=l.headers.get("x-should-retry");return i==="true"?!0:i==="false"?!1:l.status===408||l.status===409||l.status===429||l.status>=500}async retryRequest(l,i,s,f){let d;const m=f==null?void 0:f.get("retry-after-ms");if(m){const A=parseFloat(m);Number.isNaN(A)||(d=A)}const b=f==null?void 0:f.get("retry-after");if(b&&!d){const A=parseFloat(b);Number.isNaN(A)?d=Date.parse(b)-Date.now():d=A*1e3}if(!(d&&0<=d&&d<60*1e3)){const A=l.maxRetries??this.maxRetries;d=this.calculateDefaultRetryTimeoutMillis(i,A)}return await vu(d),this.makeRequest(l,i-1,s)}calculateDefaultRetryTimeoutMillis(l,i){const d=i-l,m=Math.min(.5*Math.pow(2,d),8),b=1-Math.random()*.25;return m*b*1e3}buildRequest(l,{retryCount:i=0}={}){const s={...l},{method:f,path:d,query:m,defaultBaseURL:b}=s,A=this.buildURL(d,m,b);"timeout"in s&&yv("timeout",s.timeout),s.timeout=s.timeout??this.timeout;const{bodyHeaders:v,body:R}=this.buildBody({options:s}),M=this.buildHeaders({options:l,method:f,bodyHeaders:v,retryCount:i});return{req:{method:f,headers:M,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&R instanceof globalThis.ReadableStream&&{duplex:"half"},...R&&{body:R},...this.fetchOptions??{},...s.fetchOptions??{}},url:A,timeout:s.timeout}}buildHeaders({options:l,method:i,bodyHeaders:s,retryCount:f}){let d={};this.idempotencyHeader&&i!=="get"&&(l.idempotencyKey||(l.idempotencyKey=this.defaultIdempotencyKey()),d[this.idempotencyHeader]=l.idempotencyKey);const m=W([d,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(f),...l.timeout?{"X-Stainless-Timeout":String(Math.trunc(l.timeout/1e3))}:{},...Sv(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(l),this._options.defaultHeaders,s,l.headers]);return this.validateHeaders(m),m.values}buildBody({options:{body:l,headers:i}}){if(!l)return{bodyHeaders:void 0,body:void 0};const s=W([i]);return ArrayBuffer.isView(l)||l instanceof ArrayBuffer||l instanceof DataView||typeof l=="string"&&s.values.has("content-type")||l instanceof Blob||l instanceof FormData||l instanceof URLSearchParams||globalThis.ReadableStream&&l instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:l}:typeof l=="object"&&(Symbol.asyncIterator in l||Symbol.iterator in l&&"next"in l&&typeof l.next=="function")?{bodyHeaders:void 0,body:hy(l)}:O(this,Fi,"f").call(this,{body:l,headers:s})}}Tf=gt,Fi=new WeakMap,ef=new WeakSet,Ag=function(){return this.baseURL!=="https://api.openai.com/v1"};gt.OpenAI=Tf;gt.DEFAULT_TIMEOUT=6e5;gt.OpenAIError=nt;gt.APIError=ae;gt.APIConnectionError=is;gt.APIConnectionTimeoutError=lf;gt.APIUserAbortError=qe;gt.NotFoundError=uy;gt.ConflictError=iy;gt.RateLimitError=cy;gt.BadRequestError=ay;gt.AuthenticationError=ny;gt.InternalServerError=ry;gt.PermissionDeniedError=ly;gt.UnprocessableEntityError=sy;gt.toFile=Vv;gt.Completions=ng;gt.Chat=df;gt.Embeddings=ug;gt.Files=sg;gt.Images=hg;gt.Audio=bu;gt.Moderations=yg;gt.Models=mg;gt.FineTuning=ul;gt.Graders=Sf;gt.VectorStores=ds;gt.Beta=Su;gt.Batches=Fy;gt.Uploads=wf;gt.Responses=Ef;gt.Evals=_f;gt.Containers=yf;let hn=null;const Eg=()=>(console.warn("OpenAI API key not found. Using fallback similarity calculation."),null),Ii=new Map,af=async r=>{if(hn||(hn=Eg()),!hn)throw new Error("OpenAI not available");const l=r.toLowerCase().trim();if(Ii.has(l))return Ii.get(l);try{const s=(await hn.embeddings.create({model:"text-embedding-3-large",input:l,encoding_format:"float"})).data[0].embedding;return Ii.set(l,s),s}catch(i){throw console.error("Error getting embedding:",i),i}},_p=(r,l)=>{if(r.length!==l.length)throw new Error("Vectors must have the same length");let i=0,s=0,f=0;for(let d=0;d<r.length;d++)i+=r[d]*l[d],s+=r[d]*r[d],f+=l[d]*l[d];return s=Math.sqrt(s),f=Math.sqrt(f),s===0||f===0?0:i/(s*f)},vp=async(r,l)=>{try{if(r.toLowerCase().trim()===l.toLowerCase().trim())return 1e4;const[i,s]=await Promise.all([af(r),af(l)]),f=_p(i,s);return Math.max(1,Math.floor(f*9999)+1)}catch(i){return console.error("Error calculating semantic similarity:",i),pp(r,l)}},pp=(r,l)=>{const i=r.toLowerCase().trim(),s=l.toLowerCase().trim();if(i===s)return 1e4;const d=((R,M)=>{const U=[];for(let C=0;C<=M.length;C++)U[C]=[C];for(let C=0;C<=R.length;C++)U[0][C]=C;for(let C=1;C<=M.length;C++)for(let q=1;q<=R.length;q++)M.charAt(C-1)===R.charAt(q-1)?U[C][q]=U[C-1][q-1]:U[C][q]=Math.min(U[C-1][q-1]+1,U[C][q-1]+1,U[C-1][q]+1);return U[M.length][R.length]})(i,s),m=Math.max(i.length,s.length),b=1-d/m,A=.3+Math.random()*.4,v=b*A;return Math.max(1,Math.floor(v*9999)+1)},bp=async r=>{if(hn||(hn=Eg()),!hn){console.log("OpenAI not available, skipping pre-caching");return}console.log("Pre-caching embeddings for common words...");const l=10;for(let i=0;i<r.length;i+=l){const s=r.slice(i,i+l);try{await Promise.all(s.map(f=>af(f).catch(d=>{console.warn(`Failed to cache embedding for "${f}":`,d.message)}))),i+l<r.length&&await new Promise(f=>setTimeout(f,100))}catch(f){console.warn("Error in batch pre-caching:",f)}}console.log(`Pre-cached embeddings for ${Ii.size} words`)};function Sp(){const[r,l]=ga.useState(""),[i,s]=ga.useState([]),[f,d]=ga.useState("playing"),[m,b]=ga.useState(""),[A,v]=ga.useState(!1);ga.useEffect(()=>{R(),bp(Qr.slice(0,50))},[]);const R=()=>{const U=av();l(U),s([]),d("playing"),b(""),console.log("Secret word:",U)},M=async U=>{if(!(A||f!=="playing")){v(!0);try{const C=await vp(U,r),q={word:U.toLowerCase(),score:C,id:Date.now()},Q=[...i,q].sort((Z,Et)=>Et.score-Z.score);s(Q),U.toLowerCase()===r.toLowerCase()&&d("won")}catch(C){console.error("Error calculating similarity:",C);const q=Math.floor(Math.random()*1e4)+1,Q={word:U.toLowerCase(),score:q,id:Date.now()},Z=[...i,Q].sort((Et,$)=>$.score-Et.score);s(Z)}finally{v(!1),b("")}}};return L.jsxs("div",{className:"App",children:[L.jsx(rv,{onNewGame:R,gameStatus:f,attemptCount:i.length}),L.jsx(cv,{guesses:i,currentGuess:m,setCurrentGuess:b,onGuess:M,isLoading:A,gameStatus:f,secretWord:r})]})}ev.createRoot(document.getElementById("root")).render(L.jsx(ga.StrictMode,{children:L.jsx(Sp,{})}));
//# sourceMappingURL=index-BOpzdvxx.js.map
