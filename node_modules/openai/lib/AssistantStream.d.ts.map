{"version": 3, "file": "AssistantStream.d.ts", "sourceRoot": "", "sources": ["../src/lib/AssistantStream.ts"], "names": [], "mappings": "OAAO,EAGL,OAAO,EAEP,IAAI,EACJ,SAAS,EACT,SAAS,EACT,YAAY,EAEb;OACM,EAAE,cAAc,EAAE;OAClB,EACL,GAAG,EACH,mBAAmB,EAEnB,IAAI,EACJ,8BAA8B,EAE/B;OACM,EAAE,KAAK,cAAc,EAAE;OAGvB,EACL,oBAAoB,EAIrB;OACM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE;OAClD,EAAE,4BAA4B,EAAE,OAAO,EAAE;OACzC,EAAE,UAAU,EAAE,WAAW,EAAE;AAGlC,MAAM,WAAW,qBAAsB,SAAQ,UAAU;IACvD,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC;IAGxB,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAC3C,YAAY,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IACjE,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAExC,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAC3C,YAAY,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;IACpE,WAAW,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;IAErE,eAAe,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;IAC9C,aAAa,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;IAClE,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;IAE3C,WAAW,EAAE,CAAC,OAAO,EAAE,IAAI,KAAK,IAAI,CAAC;IACrC,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,IAAI,CAAC;IACtD,QAAQ,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IAGrD,aAAa,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IAE/D,KAAK,EAAE,CAAC,KAAK,EAAE,oBAAoB,KAAK,IAAI,CAAC;CAC9C;AAED,MAAM,MAAM,kCAAkC,GAAG,IAAI,CAAC,4BAA4B,EAAE,QAAQ,CAAC,GAAG;IAC9F,MAAM,CAAC,EAAE,IAAI,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,yBAAyB,GAAG,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,GAAG;IAC5E,MAAM,CAAC,EAAE,IAAI,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,gCAAgC,GAAG,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAC,GAAG;IAC9F,MAAM,CAAC,EAAE,IAAI,CAAC;CACf,CAAC;AAEF,qBAAa,eACX,SAAQ,WAAW,CAAC,qBAAqB,CACzC,YAAW,aAAa,CAAC,oBAAoB,CAAC;;IAqB9C,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,oBAAoB,CAAC;IA8D7D,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,GAAG,eAAe;cAMlD,mBAAmB,CACjC,cAAc,EAAE,cAAc,EAC9B,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;IAiBf,gBAAgB,IAAI,cAAc;IAKlC,MAAM,CAAC,yBAAyB,CAC9B,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,gCAAgC,EACxC,OAAO,EAAE,cAAc,GAAG,SAAS,GAClC,eAAe;cAWF,0BAA0B,CACxC,GAAG,EAAE,IAAI,EACT,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,gCAAgC,EACxC,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;IAyBf,MAAM,CAAC,2BAA2B,CAChC,MAAM,EAAE,kCAAkC,EAC1C,MAAM,EAAE,OAAO,EACf,OAAO,CAAC,EAAE,cAAc,GACvB,eAAe;IAWlB,MAAM,CAAC,qBAAqB,CAC1B,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,yBAAyB,EACjC,OAAO,CAAC,EAAE,cAAc,GACvB,eAAe;IAWlB,YAAY,IAAI,oBAAoB,GAAG,SAAS;IAIhD,UAAU,IAAI,GAAG,GAAG,SAAS;IAI7B,sBAAsB,IAAI,OAAO,GAAG,SAAS;IAI7C,sBAAsB,IAAI,IAAI,CAAC,OAAO,GAAG,SAAS;IAI5C,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IAMxC,aAAa,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IAMnC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC;cAOd,4BAA4B,CAC1C,MAAM,EAAE,OAAO,EACf,MAAM,EAAE,4BAA4B,EACpC,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;cAsBC,sBAAsB,CACpC,GAAG,EAAE,IAAI,EACT,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;IAoUf,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IA2FjG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG;cAIhB,sBAAsB,CACpC,MAAM,EAAE,4BAA4B,EACpC,MAAM,EAAE,OAAO,EACf,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;cAIC,mBAAmB,CACjC,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;cAIC,uBAAuB,CACrC,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,gCAAgC,EACxC,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,GAAG,CAAC;CAGhB"}