{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/resources/index.ts"], "names": [], "mappings": ";;OAIO,EAAE,KAAK,EAAE,KAAK,UAAU,EAAE,KAAK,mBAAmB,EAAE;OACpD,EACL,OAAO,EACP,KAAK,KAAK,EACV,KAAK,UAAU,EACf,KAAK,kBAAkB,EACvB,KAAK,iBAAiB,EACtB,KAAK,eAAe,EACpB,KAAK,WAAW,GACjB;OACM,EAAE,IAAI,EAAE;OACR,EACL,WAAW,EACX,KAAK,UAAU,EACf,KAAK,gBAAgB,EACrB,KAAK,eAAe,EACpB,KAAK,sBAAsB,EAC3B,KAAK,kCAAkC,EACvC,KAAK,+BAA+B,GACrC;OACM,EACL,UAAU,EACV,KAAK,uBAAuB,EAC5B,KAAK,yBAAyB,EAC9B,KAAK,qBAAqB,EAC1B,KAAK,qBAAqB,EAC1B,KAAK,mBAAmB,EACxB,KAAK,0BAA0B,GAChC;OACM,EACL,UAAU,EACV,KAAK,uBAAuB,EAC5B,KAAK,SAAS,EACd,KAAK,cAAc,EACnB,KAAK,qBAAqB,GAC3B;OACM,EACL,KAAK,EACL,KAAK,0BAA0B,EAC/B,KAAK,qCAAqC,EAC1C,KAAK,kBAAkB,EACvB,KAAK,oBAAoB,EACzB,KAAK,kBAAkB,EACvB,KAAK,gBAAgB,EACrB,KAAK,kBAAkB,EACvB,KAAK,gBAAgB,EACrB,KAAK,gBAAgB,EACrB,KAAK,cAAc,EACnB,KAAK,qBAAqB,GAC3B;OACM,EACL,KAAK,EACL,KAAK,WAAW,EAChB,KAAK,WAAW,EAChB,KAAK,UAAU,EACf,KAAK,WAAW,EAChB,KAAK,gBAAgB,EACrB,KAAK,cAAc,EACnB,KAAK,eAAe,GACrB;OACM,EAAE,UAAU,EAAE;OACd,EAAE,OAAO,EAAE;OACX,EACL,MAAM,EACN,KAAK,KAAK,EACV,KAAK,UAAU,EACf,KAAK,cAAc,EACnB,KAAK,0BAA0B,EAC/B,KAAK,eAAe,EACpB,KAAK,mBAAmB,GACzB;OACM,EAAE,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK,UAAU,EAAE;OAC1D,EACL,WAAW,EACX,KAAK,UAAU,EACf,KAAK,uBAAuB,EAC5B,KAAK,eAAe,EACpB,KAAK,yBAAyB,EAC9B,KAAK,mBAAmB,EACxB,KAAK,wBAAwB,EAC7B,KAAK,sBAAsB,GAC5B;OACM,EAAE,SAAS,EAAE;OACb,EAAE,OAAO,EAAE,KAAK,MAAM,EAAE,KAAK,kBAAkB,EAAE,KAAK,oBAAoB,EAAE;OAC5E,EACL,YAAY,EACZ,KAAK,6BAA6B,EAClC,KAAK,oBAAoB,EACzB,KAAK,yBAAyB,EAC9B,KAAK,+BAA+B,EACpC,KAAK,0BAA0B,EAC/B,KAAK,gCAAgC,EACrC,KAAK,qCAAqC,EAC1C,KAAK,WAAW,EAChB,KAAK,kBAAkB,EACvB,KAAK,yBAAyB,EAC9B,KAAK,uBAAuB,EAC5B,KAAK,uBAAuB,EAC5B,KAAK,qBAAqB,EAC1B,KAAK,uBAAuB,EAC5B,KAAK,gBAAgB,EACrB,KAAK,8BAA8B,GACpC"}