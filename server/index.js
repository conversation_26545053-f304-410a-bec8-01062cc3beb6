import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import OpenAI from "openai";
import { getRandomWord } from "./utils/wordList.js";
import crypto from "crypto";

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Cache for embeddings to avoid repeated API calls
const embeddingCache = new Map();

// Add session management with word storage
const activeGames = new Map();

/**
 * Get embedding for a word using OpenAI's text-embedding-3-large model
 */
const getEmbedding = async (text) => {
  const cacheKey = text.toLowerCase().trim();

  // Check cache first
  if (embeddingCache.has(cacheKey)) {
    return embeddingCache.get(cacheKey);
  }

  try {
    const response = await openai.embeddings.create({
      model: "text-embedding-3-large",
      input: cacheKey,
      encoding_format: "float",
    });

    const embedding = response.data[0].embedding;

    // Cache the result
    embeddingCache.set(cacheKey, embedding);

    return embedding;
  } catch (error) {
    console.error("Error getting embedding:", error);
    throw error;
  }
};

/**
 * Calculate cosine similarity between two vectors
 */
const cosineSimilarity = (vecA, vecB) => {
  if (vecA.length !== vecB.length) {
    throw new Error("Vectors must have the same length");
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
};

/**
 * Advanced scaling function for semantic similarity
 * Maps cosine similarity (0-1) to game score (1-10000) with better granularity
 */
const scaleSemanticSimilarity = (cosineSim) => {
  // Cosine similarity for word embeddings typically ranges from ~0.1 to 0.95
  // We want to create a more exponential curve that gives high scores to very similar words

  // Apply sigmoid-like transformation to spread out high similarities
  // This gives much better granularity in the high-similarity range
  const adjusted = Math.pow(cosineSim, 0.4); // Power < 1 spreads out values

  // Apply exponential scaling to emphasize differences at the high end
  const exponential = Math.pow(adjusted, 1.5);

  // Final mapping with custom ranges for better game feel
  let score;

  if (exponential >= 0.85) {
    // Very high similarity: 8500-9999 (exponential curve)
    const range = (exponential - 0.85) / 0.15;
    score = 8500 + Math.floor(range * range * 1499); // Quadratic for top range
  } else if (exponential >= 0.7) {
    // High similarity: 6000-8499
    const range = (exponential - 0.7) / 0.15;
    score = 6000 + Math.floor(range * 2499);
  } else if (exponential >= 0.5) {
    // Medium similarity: 3000-5999
    const range = (exponential - 0.5) / 0.2;
    score = 3000 + Math.floor(range * 2999);
  } else if (exponential >= 0.3) {
    // Low similarity: 1000-2999
    const range = (exponential - 0.3) / 0.2;
    score = 1000 + Math.floor(range * 1999);
  } else {
    // Very low similarity: 1-999
    const range = exponential / 0.3;
    score = 1 + Math.floor(range * 998);
  }

  return Math.max(1, Math.min(9999, score));
};

// API endpoint for semantic similarity
app.post("/api/similarity", async (req, res) => {
  try {
    const { word1, word2 } = req.body;

    if (!word1 || !word2) {
      return res
        .status(400)
        .json({ error: "Both word1 and word2 are required" });
    }

    // Exact match gets perfect score
    if (word1.toLowerCase().trim() === word2.toLowerCase().trim()) {
      return res.json({ score: 10000, cached: false });
    }

    // Check if we have a cached result for this pair
    const cacheKey = [word1.toLowerCase().trim(), word2.toLowerCase().trim()]
      .sort()
      .join("|");

    // Get embeddings for both words
    const [embedding1, embedding2] = await Promise.all([
      getEmbedding(word1),
      getEmbedding(word2),
    ]);

    // Calculate cosine similarity
    const similarity = cosineSimilarity(embedding1, embedding2);

    // Scale to game score
    const score = scaleSemanticSimilarity(similarity);

    res.json({
      score,
      similarity: similarity.toFixed(4),
      cached:
        embeddingCache.has(word1.toLowerCase().trim()) &&
        embeddingCache.has(word2.toLowerCase().trim()),
    });
  } catch (error) {
    console.error("Error calculating similarity:", error);
    res.status(500).json({ error: "Failed to calculate similarity" });
  }
});

// Hint endpoint
app.post("/api/hint", async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: "Session ID is required" });
    }

    const gameSession = activeGames.get(sessionId);
    if (!gameSession) {
      return res.status(404).json({ error: "Game session not found" });
    }

    const word = gameSession.word;

    const prompt = `Give a 1 sentence hint about the word "${word}" but by NO MEANS can you include the word itself or any obvious variations of it. The hint should be pretty vague still and not narrow down the answer to just one word, but maybe 3 possible words.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      max_tokens: 100,
      temperature: 0.7,
    });

    let hint = response.choices[0].message.content.trim();

    // Filter out the target word and its variations
    const wordLower = word.toLowerCase();
    const wordVariations = [
      wordLower,
      wordLower + "s",
      wordLower + "ing",
      wordLower + "ed",
      wordLower + "er",
      wordLower + "est",
    ];

    // Check if hint contains the word or its variations
    const hintLower = hint.toLowerCase();
    const containsWord = wordVariations.some((variation) =>
      hintLower.includes(variation)
    );

    if (containsWord) {
      // Fallback hint if the word was detected
      hint =
        "This word is related to a common concept or object you might encounter in daily life.";
    }

    res.json({ hint });
  } catch (error) {
    console.error("Error generating hint:", error);
    res.status(500).json({ error: "Failed to generate hint" });
  }
});

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    cacheSize: embeddingCache.size,
    timestamp: new Date().toISOString(),
  });
});

// Generate a new game session
app.post("/api/game/new", (req, res) => {
  const sessionId = crypto.randomUUID();
  const secretWord = getRandomWord();

  activeGames.set(sessionId, {
    word: secretWord,
    createdAt: Date.now(),
  });

  // Clean up old sessions (optional)
  cleanupOldSessions();

  res.json({ sessionId });
});

// Check guess against secret word
app.post("/api/game/guess", async (req, res) => {
  const { sessionId, word } = req.body;

  if (!sessionId || !word) {
    return res
      .status(400)
      .json({ error: "Session ID and guess word required" });
  }

  const gameSession = activeGames.get(sessionId);
  if (!gameSession) {
    return res.status(404).json({ error: "Game session not found" });
  }

  const secretWord = gameSession.word;

  // Exact match gets perfect score
  if (word.toLowerCase().trim() === secretWord.toLowerCase().trim()) {
    return res.json({ score: 10000, isMatch: true });
  }

  // Calculate similarity
  try {
    const [embedding1, embedding2] = await Promise.all([
      getEmbedding(word),
      getEmbedding(secretWord),
    ]);

    const similarity = cosineSimilarity(embedding1, embedding2);
    const score = scaleSemanticSimilarity(similarity);

    res.json({
      score,
      similarity: similarity.toFixed(4),
      isMatch: false,
    });
  } catch (error) {
    console.error("Error calculating similarity:", error);
    res.status(500).json({ error: "Failed to calculate similarity" });
  }
});

// Helper to clean up old sessions (older than 24 hours)
function cleanupOldSessions() {
  const now = Date.now();
  const DAY_IN_MS = 24 * 60 * 60 * 1000;

  for (const [sessionId, session] of activeGames.entries()) {
    if (now - session.createdAt > DAY_IN_MS) {
      activeGames.delete(sessionId);
    }
  }
}

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Contexto backend server running on port ${PORT}`);
  console.log(`📊 Embedding cache initialized`);
});

export default app;
