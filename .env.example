# Backend API Configuration
# OpenAI API key for the backend server (keep this secure!)
OPENAI_API_KEY=your_openai_api_key_here

# Frontend Configuration
# Backend API URL (for development, use http://localhost:3001)
VITE_API_URL=http://localhost:3001

# Server Configuration
PORT=3001

# Note: The OpenAI API key is now used only by the backend server,
# keeping it secure and not exposed to the browser.
