# Contexto - Semantic Word Guessing Game

A React-based word guessing game inspired by <PERSON><PERSON>, where players try to guess a secret word using semantic similarity clues powered by OpenAI's embeddings.

## 🎮 How to Play

1. **Objective**: Guess the secret word with as few attempts as possible
2. **Scoring**: Each guess receives a score from 1 (cold) to 10,000 (exact match) based on semantic similarity
3. **Strategy**: Use the similarity scores to guide your next guesses toward words that are semantically related
4. **Victory**: Find the exact word to win the game!

## 🚀 Features

- **Semantic Similarity**: Uses OpenAI's text-embedding-3-large model for accurate word relationships
- **Wordle-Inspired Design**: Clean, modern UI with familiar color schemes and animations
- **Real-time Scoring**: Instant feedback with visual score bars and temperature indicators
- **Shareable Results**: Copy your game results to share with friends
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Performance Optimized**: Embedding caching and efficient rendering

## 🛠️ Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- OpenAI API key (optional - falls back to string similarity)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd contexto
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure OpenAI API (Optional)**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your OpenAI API key:
   ```
   VITE_OPENAI_API_KEY=your_actual_api_key_here
   ```
   
   Get your API key from: https://platform.openai.com/api-keys

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory, ready for deployment.

## 🚀 Deployment on Vercel

1. **Push your code to GitHub**

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Vercel will automatically detect it's a Vite project

3. **Add Environment Variables**
   - In your Vercel project settings, add:
   - `VITE_OPENAI_API_KEY` = your OpenAI API key

4. **Deploy**
   - Vercel will automatically build and deploy your app
   - Your game will be live at `https://your-project.vercel.app`

## 🎨 Customization

### Adding New Words
Edit `src/utils/wordList.js` to add or modify the secret word pool.

### Styling
The app uses CSS custom properties for easy theming. Modify the color scheme in `src/index.css`:

```css
:root {
  --color-primary: #6aaa64;    /* Main green color */
  --color-secondary: #c9b458;  /* Yellow color */
  --color-warning: #f5a623;    /* Orange color */
  /* ... more colors */
}
```

### Scoring Algorithm
Modify the scoring logic in `src/services/semanticSimilarity.js` to adjust how similarity is calculated and scaled.

## 🔧 Technical Details

### Architecture
- **Frontend**: React 18 with Vite
- **Styling**: CSS with custom properties for theming
- **AI**: OpenAI text-embedding-3-large model
- **State Management**: React hooks (useState, useEffect)
- **Performance**: Embedding caching, optimized re-renders

### API Usage
- Uses OpenAI's embeddings API for semantic similarity
- Implements cosine similarity calculation
- Falls back to string-based similarity if API is unavailable
- Caches embeddings to reduce API calls

### Security Note
⚠️ **Important**: This implementation includes the OpenAI API key in the frontend for simplicity. In a production environment, you should:
- Move API calls to a backend server
- Keep API keys secure on the server side
- Implement rate limiting and user authentication

## 📝 License

MIT License - feel free to use this project for learning or building your own word games!

## 🤝 Contributing

Contributions are welcome! Feel free to:
- Report bugs
- Suggest new features
- Submit pull requests
- Improve documentation

## 🎯 Future Enhancements

- [ ] Multiplayer mode
- [ ] Daily challenges
- [ ] Difficulty levels
- [ ] More word categories
- [ ] Statistics tracking
- [ ] Social sharing improvements
- [ ] Offline mode with pre-computed similarities
