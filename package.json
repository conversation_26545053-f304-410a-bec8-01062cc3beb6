{"name": "contexto", "version": "1.0.0", "description": "A semantic word guessing game inspired by <PERSON><PERSON>", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/index.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@vitejs/plugin-react": "^4.5.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "openai": "^5.5.1", "react": "^19.1.0", "react-dom": "^19.1.0", "vite": "^6.3.5"}, "devDependencies": {"concurrently": "^9.1.2"}}