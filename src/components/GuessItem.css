.guess-item {
  position: relative;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
  animation: slideIn 0.3s ease-out;
}

.guess-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.guess-item.winning {
  border-color: var(--color-primary);
  background: linear-gradient(
    135deg,
    rgba(106, 170, 100, 0.05) 0%,
    rgba(106, 170, 100, 0.1) 100%
  );
  animation: bounce 1s ease-in-out;
}

.guess-item.recent {
  border-color: var(--color-primary);
  background: linear-gradient(
    135deg,
    rgba(106, 170, 100, 0.08) 0%,
    rgba(106, 170, 100, 0.15) 100%
  );
  box-shadow: 0 2px 8px rgba(106, 170, 100, 0.2);
  animation: slideIn 0.4s ease-out;
}

.guess-item.recent .rank {
  background-color: var(--color-primary);
  color: white;
}

.guess-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.guess-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.rank {
  background-color: var(--color-gray);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 700;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  min-width: 2rem;
  text-align: center;
  flex-shrink: 0;
}

.word {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text);
  text-transform: capitalize;
  flex: 1;
  min-width: 0;
}

.temperature {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.score-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.score {
  font-size: var(--font-size-base);
  font-weight: 700;
  color: var(--color-text);
  min-width: 4rem;
  text-align: right;
  flex-shrink: 0;
}

.score-bar-container {
  flex: 1;
  height: 8px;
  background-color: var(--color-light-gray);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
}

.score-bar {
  height: 100%;
  border-radius: var(--radius-md);
  transition: width 0.6s ease-out;
  position: relative;
}

.score-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.winning-animation {
  position: absolute;
  top: -10px;
  right: -10px;
  display: flex;
  gap: var(--spacing-xs);
  pointer-events: none;
}

.confetti {
  font-size: var(--font-size-lg);
  animation: confetti 2s ease-out infinite;
}

.confetti:nth-child(2) {
  animation-delay: 0.3s;
}

.confetti:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes confetti {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-30px) rotate(360deg);
    opacity: 0;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .guess-item {
    padding: var(--spacing-sm);
  }

  .guess-info {
    gap: var(--spacing-sm);
  }

  .word {
    font-size: var(--font-size-base);
  }

  .temperature {
    font-size: var(--font-size-xs);
  }

  .score-section {
    gap: var(--spacing-sm);
  }

  .score {
    font-size: var(--font-size-sm);
    min-width: 3rem;
  }
}
