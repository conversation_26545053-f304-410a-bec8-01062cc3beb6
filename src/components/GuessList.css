.guesses-container {
  background-color: var(--color-surface);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
}

.no-guesses {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  color: var(--color-text-secondary);
}

.no-guesses p {
  font-size: var(--font-size-lg);
  font-style: italic;
}

.guesses-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.guesses-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
}

.ranking-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-style: italic;
}

.recent-guess-section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: linear-gradient(
    135deg,
    rgba(106, 170, 100, 0.05) 0%,
    rgba(106, 170, 100, 0.1) 100%
  );
  border: 1px solid rgba(106, 170, 100, 0.2);
  border-radius: var(--radius-lg);
}

.recent-guess-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.sorted-guesses-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--spacing-md);
  text-align: center;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border);
}

.guesses-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 60vh;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
}

/* Custom scrollbar for guess list */
.guesses-list::-webkit-scrollbar {
  width: 6px;
}

.guesses-list::-webkit-scrollbar-track {
  background: var(--color-background);
  border-radius: var(--radius-md);
}

.guesses-list::-webkit-scrollbar-thumb {
  background: var(--color-light-gray);
  border-radius: var(--radius-md);
}

.guesses-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray);
}

/* Responsive design */
@media (max-width: 768px) {
  .guesses-container {
    padding: var(--spacing-md);
  }

  .guesses-list {
    max-height: 50vh;
  }

  .no-guesses {
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .no-guesses p {
    font-size: var(--font-size-base);
  }
}
