.guess-input-container {
  background-color: var(--color-surface);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
}

.guess-form {
  margin-bottom: var(--spacing-lg);
}

.input-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.guess-input {
  flex: 1;
  padding: var(--spacing-md);
  font-size: var(--font-size-lg);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  background-color: var(--color-background);
  color: var(--color-text);
  transition: all var(--transition-fast);
  min-height: 48px;
}

.guess-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(106, 170, 100, 0.1);
}

.guess-input.error {
  border-color: var(--color-danger);
}

.guess-input.error:focus {
  border-color: var(--color-danger);
  box-shadow: 0 0 0 3px rgba(215, 48, 39, 0.1);
}

.guess-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.guess-submit {
  background-color: var(--color-primary);
  color: white;
  border: 2px solid var(--color-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 48px;
  white-space: nowrap;
}

.guess-submit:hover:not(:disabled) {
  background-color: #5a9a54;
  border-color: #5a9a54;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.guess-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--font-size-sm);
  font-weight: 500;
  animation: fadeIn 0.3s ease-out;
}

.hint-section {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.hint-btn {
  background-color: var(--color-secondary);
  color: white;
  border: 2px solid var(--color-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.hint-btn:hover:not(:disabled) {
  background-color: #b8a347;
  border-color: #b8a347;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.hint-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.hint-display {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: rgba(201, 180, 88, 0.1);
  border: 1px solid rgba(201, 180, 88, 0.3);
  border-radius: var(--radius-md);
  color: var(--color-text);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  animation: fadeIn 0.3s ease-out;
}

.hint-display strong {
  color: var(--color-secondary);
}

.instructions {
  border-top: 1px solid var(--color-border);
  padding-top: var(--spacing-md);
}

.instructions p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.5;
}

.instructions p:last-child {
  margin-bottom: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .guess-input-container {
    padding: var(--spacing-md);
  }

  .input-group {
    flex-direction: column;
  }

  .guess-input {
    font-size: var(--font-size-base);
  }

  .guess-submit {
    width: 100%;
    padding: var(--spacing-md);
  }
}
