import React, { useState, useRef, useEffect } from "react";
import { isValidWord } from "../utils/wordList";
import "./GuessInput.css";

const GuessInput = ({
  currentGuess,
  setCurrentGuess,
  onGuess,
  isLoading,
  guesses,
  hint,
  isLoadingHint,
  onGetHint,
}) => {
  const [error, setError] = useState("");
  const inputRef = useRef(null);

  // Maintain focus on input after guess
  useEffect(() => {
    if (inputRef.current && !isLoading) {
      inputRef.current.focus();
    }
  }, [isLoading, currentGuess]);

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!currentGuess.trim()) {
      setError("Please enter a word");
      return;
    }

    if (!isValidWord(currentGuess)) {
      setError(
        "Please enter a valid word (letters only, at least 2 characters)"
      );
      return;
    }

    // Check if word already guessed
    if (guesses.some((guess) => guess.word === currentGuess.toLowerCase())) {
      setError("You already guessed this word!");
      return;
    }

    setError("");
    onGuess(currentGuess);
  };

  const handleInputChange = (e) => {
    setCurrentGuess(e.target.value);
    if (error) setError("");
  };

  return (
    <div className="guess-input-container">
      <form onSubmit={handleSubmit} className="guess-form">
        <div className="input-group">
          <input
            ref={inputRef}
            type="text"
            value={currentGuess}
            onChange={handleInputChange}
            placeholder="Enter your guess..."
            className={`guess-input ${error ? "error" : ""}`}
            disabled={isLoading}
            autoFocus
          />
          <button
            type="submit"
            className="guess-submit"
            disabled={isLoading || !currentGuess.trim()}
          >
            {isLoading ? "Thinking..." : "Guess"}
          </button>
        </div>
        {error && <div className="error-message">{error}</div>}
      </form>

      <div className="hint-section">
        <button
          className="hint-btn"
          onClick={onGetHint}
          disabled={isLoadingHint}
        >
          {isLoadingHint ? "Getting hint..." : "💡 Get Hint"}
        </button>
        {hint && (
          <div className="hint-display">
            <strong>Hint:</strong> {hint}
          </div>
        )}
      </div>

      <div className="instructions">
        <p>
          💡 <strong>How to play:</strong> Guess words related to the secret
          word. Higher scores mean you're closer!
        </p>
        <p>🎯 Score ranges from 1 (cold) to 10,000 (exact match)</p>
      </div>
    </div>
  );
};

export default GuessInput;
