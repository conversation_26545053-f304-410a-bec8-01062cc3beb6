import React from "react";
import GuessInput from "./GuessInput";
import GuessList from "./GuessList";
import GameResult from "./GameResult";
import "./GameBoard.css";

const GameBoard = ({
  guesses,
  currentGuess,
  setCurrentGuess,
  onGuess,
  isLoading,
  gameStatus,
  secretWord,
  hint,
  isLoadingHint,
  onGetHint,
  mostRecentGuess,
}) => {
  return (
    <main className="game-board">
      <div className="game-container">
        {gameStatus === "playing" && (
          <GuessInput
            currentGuess={currentGuess}
            setCurrentGuess={setCurrentGuess}
            onGuess={onGuess}
            isLoading={isLoading}
            guesses={guesses}
            hint={hint}
            isLoadingHint={isLoadingHint}
            onGetHint={onGetHint}
          />
        )}

        {gameStatus === "won" && (
          <GameResult
            secretWord={secretWord}
            attemptCount={guesses.length}
            guesses={guesses}
          />
        )}

        <GuessList guesses={guesses} mostRecentGuess={mostRecentGuess} />
      </div>
    </main>
  );
};

export default GameBoard;
