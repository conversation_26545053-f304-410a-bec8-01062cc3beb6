import React from "react";
import "./GuessItem.css";

const GuessItem = ({ guess, rank, isWinning, isRecent = false }) => {
  const getScoreColor = (score) => {
    if (score === 10000) return "#6aaa64"; // Green for exact match
    if (score >= 8000) return "#c9b458"; // Yellow for very close
    if (score >= 6000) return "#f5a623"; // Orange for close
    if (score >= 4000) return "#d73027"; // Red for somewhat close
    if (score >= 2000) return "#9c27b0"; // Purple for distant
    return "#787c7e"; // Gray for very distant
  };

  const getScoreWidth = (score) => {
    return Math.max((score / 10000) * 100, 2); // Minimum 2% width for visibility
  };

  const getTemperature = (score) => {
    if (score === 10000) return "🔥 PERFECT!";
    if (score >= 9000) return "🔥 Burning hot!";
    if (score >= 8000) return "🌡️ Very hot";
    if (score >= 7000) return "🌡️ Hot";
    if (score >= 6000) return "🌡️ Warm";
    if (score >= 4000) return "🌡️ Cool";
    if (score >= 2000) return "❄️ Cold";
    return "🧊 Freezing";
  };

  return (
    <div
      className={`guess-item ${isWinning ? "winning" : ""} ${
        isRecent ? "recent" : ""
      }`}
    >
      <div className="guess-content">
        <div className="guess-info">
          <span className="rank">#{rank}</span>
          <span className="word">{guess.word}</span>
          <span className="temperature">{getTemperature(guess.score)}</span>
        </div>

        <div className="score-section">
          <span className="score">{guess.score.toLocaleString()}</span>
          <div className="score-bar-container">
            <div
              className="score-bar"
              style={{
                width: `${getScoreWidth(guess.score)}%`,
                backgroundColor: getScoreColor(guess.score),
              }}
            />
          </div>
        </div>
      </div>

      {isWinning && (
        <div className="winning-animation">
          <span className="confetti">🎉</span>
          <span className="confetti">🎊</span>
          <span className="confetti">✨</span>
        </div>
      )}
    </div>
  );
};

export default GuessItem;
