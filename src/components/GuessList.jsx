import React from "react";
import GuessItem from "./GuessItem";
import "./GuessList.css";

const GuessList = ({ guesses, mostRecentGuess }) => {
  if (guesses.length === 0) {
    return (
      <div className="guesses-container">
        <div className="no-guesses">
          <p>No guesses yet. Start by entering a word above!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="guesses-container">
      <div className="guesses-header">
        <h3>Your Guesses ({guesses.length})</h3>
        <p className="ranking-info">
          Most recent guess shown first, then ranked by similarity
        </p>
      </div>

      {/* Show most recent guess at the top if it exists */}
      {mostRecentGuess && (
        <div className="recent-guess-section">
          <h4 className="recent-guess-title">🔥 Most Recent Guess</h4>
          <GuessItem
            guess={mostRecentGuess}
            rank={guesses.findIndex((g) => g.id === mostRecentGuess.id) + 1}
            isWinning={mostRecentGuess.score === 10000}
            isRecent={true}
          />
        </div>
      )}

      <div className="guesses-list">
        <h4 className="sorted-guesses-title">
          📊 All Guesses (Ranked by Score)
        </h4>
        {guesses.map((guess, index) => (
          <GuessItem
            key={guess.id}
            guess={guess}
            rank={index + 1}
            isWinning={guess.score === 10000}
            isRecent={false}
          />
        ))}
      </div>
    </div>
  );
};

export default GuessList;
