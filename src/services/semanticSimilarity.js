import OpenAI from "openai";

// Initialize OpenAI client
let openai = null;

const initializeOpenAI = () => {
  const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
  if (!apiKey) {
    console.warn(
      "OpenAI API key not found. Using fallback similarity calculation."
    );
    return null;
  }

  try {
    openai = new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true, // Note: In production, this should be done server-side
    });
    return openai;
  } catch (error) {
    console.error("Failed to initialize OpenAI:", error);
    return null;
  }
};

// Cache for embeddings to avoid repeated API calls
const embeddingCache = new Map();

/**
 * Get embedding for a word using OpenAI's text-embedding-3-large model
 */
const getEmbedding = async (text) => {
  if (!openai) {
    openai = initializeOpenAI();
  }

  if (!openai) {
    throw new Error("OpenAI not available");
  }

  const cacheKey = text.toLowerCase().trim();

  // Check cache first
  if (embeddingCache.has(cacheKey)) {
    return embeddingCache.get(cacheKey);
  }

  try {
    const response = await openai.embeddings.create({
      model: "text-embedding-3-large",
      input: cacheKey,
      encoding_format: "float",
    });

    const embedding = response.data[0].embedding;

    // Cache the result
    embeddingCache.set(cacheKey, embedding);

    return embedding;
  } catch (error) {
    console.error("Error getting embedding:", error);
    throw error;
  }
};

/**
 * Calculate cosine similarity between two vectors
 */
const cosineSimilarity = (vecA, vecB) => {
  if (vecA.length !== vecB.length) {
    throw new Error("Vectors must have the same length");
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
};

/**
 * Calculate semantic similarity between two words
 * Returns a score from 1 to 10,000
 */
export const calculateSemanticSimilarity = async (word1, word2) => {
  try {
    // Exact match gets perfect score
    if (word1.toLowerCase().trim() === word2.toLowerCase().trim()) {
      return 10000;
    }

    // Get embeddings for both words
    const [embedding1, embedding2] = await Promise.all([
      getEmbedding(word1),
      getEmbedding(word2),
    ]);

    // Calculate cosine similarity
    const similarity = cosineSimilarity(embedding1, embedding2);

    // Convert to 1-10,000 scale with better distribution
    // Cosine similarity for word embeddings typically ranges from ~0.1 to 0.95
    // We want to spread this range more linearly across our 1-10,000 scale

    // Map to 1-10,000 scale with better distribution
    // Very low similarities (< 0.2) get scores 1-1000
    // Medium similarities (0.2-0.6) get scores 1000-6000
    // High similarities (0.6-0.9) get scores 6000-9500
    // Very high similarities (0.9+) get scores 9500-9999

    let score;
    if (similarity < 0.2) {
      // Very unrelated words: 1-1000
      score = Math.floor((similarity / 0.2) * 999) + 1;
    } else if (similarity < 0.6) {
      // Somewhat related words: 1000-6000
      score = Math.floor(((similarity - 0.2) / 0.4) * 5000) + 1000;
    } else if (similarity < 0.9) {
      // Related words: 6000-9500
      score = Math.floor(((similarity - 0.6) / 0.3) * 3500) + 6000;
    } else {
      // Very related words: 9500-9999
      score = Math.floor(((similarity - 0.9) / 0.1) * 499) + 9500;
    }

    return Math.max(1, Math.min(9999, score));
  } catch (error) {
    console.error("Error calculating semantic similarity:", error);
    // Fallback to simple string similarity
    return calculateFallbackSimilarity(word1, word2);
  }
};

/**
 * Fallback similarity calculation when OpenAI is not available
 * Uses simple string-based similarity
 */
const calculateFallbackSimilarity = (word1, word2) => {
  const w1 = word1.toLowerCase().trim();
  const w2 = word2.toLowerCase().trim();

  // Exact match
  if (w1 === w2) {
    return 10000;
  }

  // Calculate Levenshtein distance
  const levenshteinDistance = (str1, str2) => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  const distance = levenshteinDistance(w1, w2);
  const maxLength = Math.max(w1.length, w2.length);
  const similarity = 1 - distance / maxLength;

  // Create more realistic semantic-like scoring for fallback
  // Check for common word patterns and relationships
  let semanticBonus = 0;

  // Time-related words
  const timeWords = [
    "morning",
    "afternoon",
    "evening",
    "night",
    "day",
    "week",
    "month",
    "year",
  ];
  if (timeWords.includes(w1) && timeWords.includes(w2)) {
    semanticBonus = 0.6; // High similarity for time words
  }

  // Color words
  const colorWords = [
    "red",
    "blue",
    "green",
    "yellow",
    "orange",
    "purple",
    "pink",
    "black",
    "white",
    "brown",
  ];
  if (colorWords.includes(w1) && colorWords.includes(w2)) {
    semanticBonus = 0.5;
  }

  // Animal words
  const animalWords = [
    "cat",
    "dog",
    "bird",
    "fish",
    "horse",
    "elephant",
    "tiger",
    "lion",
    "bear",
    "wolf",
  ];
  if (animalWords.includes(w1) && animalWords.includes(w2)) {
    semanticBonus = 0.5;
  }

  // Food words
  const foodWords = [
    "apple",
    "banana",
    "bread",
    "cheese",
    "pizza",
    "cake",
    "chocolate",
    "coffee",
    "tea",
  ];
  if (foodWords.includes(w1) && foodWords.includes(w2)) {
    semanticBonus = 0.4;
  }

  const finalSimilarity = Math.min(0.95, similarity + semanticBonus);

  // Use the same scoring logic as the real function
  let score;
  if (finalSimilarity < 0.2) {
    score = Math.floor((finalSimilarity / 0.2) * 999) + 1;
  } else if (finalSimilarity < 0.6) {
    score = Math.floor(((finalSimilarity - 0.2) / 0.4) * 5000) + 1000;
  } else if (finalSimilarity < 0.9) {
    score = Math.floor(((finalSimilarity - 0.6) / 0.3) * 3500) + 6000;
  } else {
    score = Math.floor(((finalSimilarity - 0.9) / 0.1) * 499) + 9500;
  }

  return Math.max(1, Math.min(9999, score));
};

/**
 * Pre-cache embeddings for common words to improve performance
 */
export const preCacheCommonWords = async (words) => {
  if (!openai) {
    openai = initializeOpenAI();
  }

  if (!openai) {
    console.log("OpenAI not available, skipping pre-caching");
    return;
  }

  console.log("Pre-caching embeddings for common words...");

  // Process words in batches to avoid rate limits
  const batchSize = 10;
  for (let i = 0; i < words.length; i += batchSize) {
    const batch = words.slice(i, i + batchSize);

    try {
      await Promise.all(
        batch.map((word) =>
          getEmbedding(word).catch((err) => {
            console.warn(
              `Failed to cache embedding for "${word}":`,
              err.message
            );
          })
        )
      );

      // Small delay between batches
      if (i + batchSize < words.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.warn("Error in batch pre-caching:", error);
    }
  }

  console.log(`Pre-cached embeddings for ${embeddingCache.size} words`);
};
