// Backend API configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:3001";

// Cache for similarity scores to avoid repeated API calls
const similarityCache = new Map();

/**
 * Calculate semantic similarity between two words using backend API
 * Returns a score from 1 to 10,000
 */
export const calculateSemanticSimilarity = async (word1, word2) => {
  try {
    // Exact match gets perfect score
    if (word1.toLowerCase().trim() === word2.toLowerCase().trim()) {
      return 10000;
    }

    // Create a cache key for this word pair
    const cacheKey = [word1.toLowerCase().trim(), word2.toLowerCase().trim()]
      .sort()
      .join("|");

    // Check cache first
    if (similarityCache.has(cacheKey)) {
      return similarityCache.get(cacheKey);
    }

    // Call backend API
    const response = await fetch(`${API_BASE_URL}/api/similarity`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        word1: word1.trim(),
        word2: word2.trim(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status}`);
    }

    const data = await response.json();
    const score = data.score;

    // Validate the score
    if (typeof score !== "number" || score < 1 || score > 10000) {
      throw new Error(`Invalid score from backend: ${score}`);
    }

    // Cache the result
    similarityCache.set(cacheKey, score);

    return score;
  } catch (error) {
    console.error("Error calculating semantic similarity:", error);
    // Fallback to simple string similarity
    return calculateFallbackSimilarity(word1, word2);
  }
};

/**
 * Fallback similarity calculation when OpenAI is not available
 * Uses simple string-based similarity
 */
const calculateFallbackSimilarity = (word1, word2) => {
  const w1 = word1.toLowerCase().trim();
  const w2 = word2.toLowerCase().trim();

  // Exact match
  if (w1 === w2) {
    return 10000;
  }

  // Calculate Levenshtein distance
  const levenshteinDistance = (str1, str2) => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  const distance = levenshteinDistance(w1, w2);
  const maxLength = Math.max(w1.length, w2.length);
  const similarity = 1 - distance / maxLength;

  // Create more realistic semantic-like scoring for fallback
  // Check for common word patterns and relationships
  let semanticBonus = 0;

  // Time-related words
  const timeWords = [
    "morning",
    "afternoon",
    "evening",
    "night",
    "day",
    "week",
    "month",
    "year",
  ];
  if (timeWords.includes(w1) && timeWords.includes(w2)) {
    semanticBonus = 0.6; // High similarity for time words
  }

  // Color words
  const colorWords = [
    "red",
    "blue",
    "green",
    "yellow",
    "orange",
    "purple",
    "pink",
    "black",
    "white",
    "brown",
  ];
  if (colorWords.includes(w1) && colorWords.includes(w2)) {
    semanticBonus = 0.5;
  }

  // Animal words
  const animalWords = [
    "cat",
    "dog",
    "bird",
    "fish",
    "horse",
    "elephant",
    "tiger",
    "lion",
    "bear",
    "wolf",
  ];
  if (animalWords.includes(w1) && animalWords.includes(w2)) {
    semanticBonus = 0.5;
  }

  // Food words
  const foodWords = [
    "apple",
    "banana",
    "bread",
    "cheese",
    "pizza",
    "cake",
    "chocolate",
    "coffee",
    "tea",
  ];
  if (foodWords.includes(w1) && foodWords.includes(w2)) {
    semanticBonus = 0.4;
  }

  const finalSimilarity = Math.min(0.95, similarity + semanticBonus);

  // Use the same scoring logic as the real function
  let score;
  if (finalSimilarity < 0.2) {
    score = Math.floor((finalSimilarity / 0.2) * 999) + 1;
  } else if (finalSimilarity < 0.6) {
    score = Math.floor(((finalSimilarity - 0.2) / 0.4) * 5000) + 1000;
  } else if (finalSimilarity < 0.9) {
    score = Math.floor(((finalSimilarity - 0.6) / 0.3) * 3500) + 6000;
  } else {
    score = Math.floor(((finalSimilarity - 0.9) / 0.1) * 499) + 9500;
  }

  return Math.max(1, Math.min(9999, score));
};

/**
 * Initialize similarity cache and check backend health
 */
export const preCacheCommonWords = async () => {
  try {
    // Check if backend is available
    const response = await fetch(`${API_BASE_URL}/api/health`);
    if (response.ok) {
      const data = await response.json();
      console.log("✅ Backend API is available");
      console.log(`📊 Backend cache size: ${data.cacheSize} embeddings`);
    } else {
      console.warn(
        "⚠️ Backend API not responding, will use fallback similarity"
      );
    }
  } catch (error) {
    console.warn("⚠️ Backend API not available, will use fallback similarity");
  }

  console.log(
    `🎯 Frontend cache ready. Current size: ${similarityCache.size} pairs`
  );
};
