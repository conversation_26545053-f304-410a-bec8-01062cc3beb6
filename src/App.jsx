import { useState, useEffect } from "react";
import "./App.css";
import GameBoard from "./components/GameBoard";
import Header from "./components/Header";
import { getRandomWord, WORD_LIST } from "./utils/wordList";
import {
  calculateSemanticSimilarity,
  preCacheCommonWords,
} from "./services/semanticSimilarity";

function App() {
  const [secretWord, setSecretWord] = useState("");
  const [guesses, setGuesses] = useState([]);
  const [gameStatus, setGameStatus] = useState("playing"); // 'playing', 'won', 'lost'
  const [currentGuess, setCurrentGuess] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Initialize game and pre-cache embeddings
  useEffect(() => {
    startNewGame();

    // Pre-cache embeddings for better performance
    preCacheCommonWords(WORD_LIST.slice(0, 50)); // Cache first 50 words
  }, []);

  const startNewGame = () => {
    const newWord = getRandomWord();
    setSecretWord(newWord);
    setGuesses([]);
    setGameStatus("playing");
    setCurrentGuess("");
    console.log("Secret word:", newWord); // For development - remove in production
  };

  const addGuess = async (word) => {
    if (isLoading || gameStatus !== "playing") return;

    setIsLoading(true);

    try {
      // Calculate similarity score using semantic embeddings
      const score = await calculateSemanticSimilarity(word, secretWord);

      const newGuess = {
        word: word.toLowerCase(),
        score,
        id: Date.now(),
      };

      const updatedGuesses = [...guesses, newGuess].sort(
        (a, b) => b.score - a.score
      );
      setGuesses(updatedGuesses);

      // Check if won
      if (word.toLowerCase() === secretWord.toLowerCase()) {
        setGameStatus("won");
      }
    } catch (error) {
      console.error("Error calculating similarity:", error);
      // Fallback to random score for development
      const fallbackScore = Math.floor(Math.random() * 10000) + 1;
      const newGuess = {
        word: word.toLowerCase(),
        score: fallbackScore,
        id: Date.now(),
      };
      const updatedGuesses = [...guesses, newGuess].sort(
        (a, b) => b.score - a.score
      );
      setGuesses(updatedGuesses);
    } finally {
      setIsLoading(false);
      setCurrentGuess("");
    }
  };

  return (
    <div className="App">
      <Header
        onNewGame={startNewGame}
        gameStatus={gameStatus}
        attemptCount={guesses.length}
      />
      <GameBoard
        guesses={guesses}
        currentGuess={currentGuess}
        setCurrentGuess={setCurrentGuess}
        onGuess={addGuess}
        isLoading={isLoading}
        gameStatus={gameStatus}
        secretWord={secretWord}
      />
    </div>
  );
}

export default App;
