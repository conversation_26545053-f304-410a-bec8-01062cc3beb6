import { useState, useEffect } from "react";
import "./App.css";
import GameBoard from "./components/GameBoard";
import Header from "./components/Header";
import { getRandomWord } from "./utils/wordList";
import {
  createNewGame,
  makeGuess,
  preCacheCommonWords,
  getHint,
} from "./services/semanticSimilarity";

function App() {
  const [sessionId, setSessionId] = useState(null);
  const [guesses, setGuesses] = useState([]);
  const [gameStatus, setGameStatus] = useState("playing");
  const [currentGuess, setCurrentGuess] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [hint, setHint] = useState("");
  const [isLoadingHint, setIsLoadingHint] = useState(false);
  const [mostRecentGuess, setMostRecentGuess] = useState(null);

  // Initialize game
  useEffect(() => {
    startNewGame();
    preCacheCommonWords();
  }, []);

  const startNewGame = async () => {
    try {
      const newSessionId = await createNewGame();
      setSessionId(newSessionId);
      setGuesses([]);
      setGameStatus("playing");
      setCurrentGuess("");
      setHint("");
      setMostRecentGuess(null);
      console.log("New game session:", newSessionId);
    } catch (error) {
      console.error("Error starting new game:", error);
      // Fallback to local game
      const newWord = getRandomWord();
      setSecretWord(newWord);
      setSessionId(null);
      console.log("Fallback - Secret word:", newWord);
    }
  };

  const addGuess = async (word) => {
    if (isLoading || gameStatus !== "playing") return;

    setIsLoading(true);

    try {
      let data;

      if (sessionId) {
        // Use backend session
        data = await makeGuess(sessionId, word);
      } else {
        // Fallback to local calculation (shouldn't happen normally)
        const fallbackScore = Math.floor(Math.random() * 10000) + 1;
        data = { score: fallbackScore, isMatch: false };
      }

      const newGuess = {
        word: word.toLowerCase(),
        score: data.score,
        id: Date.now(),
      };

      const updatedGuesses = [...guesses, newGuess].sort(
        (a, b) => b.score - a.score
      );
      setGuesses(updatedGuesses);
      setMostRecentGuess(newGuess);

      // Check if won
      if (data.isMatch) {
        setGameStatus("won");
      }
    } catch (error) {
      console.error("Error processing guess:", error);
      // Fallback handling
      const fallbackScore = Math.floor(Math.random() * 10000) + 1;
      const newGuess = {
        word: word.toLowerCase(),
        score: fallbackScore,
        id: Date.now(),
      };
      const updatedGuesses = [...guesses, newGuess].sort(
        (a, b) => b.score - a.score
      );
      setGuesses(updatedGuesses);
      setMostRecentGuess(newGuess);
    } finally {
      setIsLoading(false);
      setCurrentGuess("");
    }
  };

  const handleGetHint = async () => {
    if (isLoadingHint || gameStatus !== "playing" || !sessionId) return;

    setIsLoadingHint(true);
    try {
      const hintText = await getHint(sessionId);
      setHint(hintText);
    } catch (error) {
      console.error("Error getting hint:", error);
      setHint("Try thinking of words related to common everyday concepts.");
    } finally {
      setIsLoadingHint(false);
    }
  };

  return (
    <div className="App">
      <Header
        onNewGame={startNewGame}
        gameStatus={gameStatus}
        attemptCount={guesses.length}
      />
      <GameBoard
        guesses={guesses}
        currentGuess={currentGuess}
        setCurrentGuess={setCurrentGuess}
        onGuess={addGuess}
        isLoading={isLoading}
        gameStatus={gameStatus}
        secretWord={secretWord}
        hint={hint}
        isLoadingHint={isLoadingHint}
        onGetHint={handleGetHint}
        mostRecentGuess={mostRecentGuess}
      />
    </div>
  );
}

export default App;
