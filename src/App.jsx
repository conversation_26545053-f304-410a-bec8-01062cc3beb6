import { useState, useEffect } from "react";
import "./App.css";
import GameBoard from "./components/GameBoard";
import Header from "./components/Header";
import { getRandomWord } from "./utils/wordList";
import {
  calculateSemanticSimilarity,
  preCacheCommonWords,
  getHint,
} from "./services/semanticSimilarity";

function App() {
  const [sessionId, setSessionId] = useState(null);
  const [guesses, setGuesses] = useState([]);
  const [gameStatus, setGameStatus] = useState("playing");
  const [currentGuess, setCurrentGuess] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [hint, setHint] = useState("");
  const [isLoadingHint, setIsLoadingHint] = useState(false);
  const [mostRecentGuess, setMostRecentGuess] = useState(null);

  // Initialize game
  useEffect(() => {
    startNewGame();
    preCacheCommonWords();
  }, []);

  const startNewGame = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/game/new`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        }
      );

      if (!response.ok) throw new Error("Failed to start new game");

      const data = await response.json();
      setSessionId(data.sessionId);
      setGuesses([]);
      setGameStatus("playing");
      setCurrentGuess("");
      setHint("");
      setMostRecentGuess(null);
    } catch (error) {
      console.error("Error starting new game:", error);
    }
  };

  const addGuess = async (word) => {
    if (isLoading || gameStatus !== "playing" || !sessionId) return;

    setIsLoading(true);

    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/game/guess`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ sessionId, word: word.trim() }),
        }
      );

      if (!response.ok) throw new Error("Failed to process guess");

      const data = await response.json();

      const newGuess = {
        word: word.toLowerCase(),
        score: data.score,
        id: Date.now(),
      };

      const updatedGuesses = [...guesses, newGuess].sort(
        (a, b) => b.score - a.score
      );
      setGuesses(updatedGuesses);
      setMostRecentGuess(newGuess);

      // Check if won
      if (data.isMatch) {
        setGameStatus("won");
      }
    } catch (error) {
      console.error("Error processing guess:", error);
      // Fallback handling
      const fallbackScore = Math.floor(Math.random() * 10000) + 1;
      const newGuess = {
        word: word.toLowerCase(),
        score: fallbackScore,
        id: Date.now(),
      };
      const updatedGuesses = [...guesses, newGuess].sort(
        (a, b) => b.score - a.score
      );
      setGuesses(updatedGuesses);
    } finally {
      setIsLoading(false);
      setCurrentGuess("");
    }
  };

  const handleGetHint = async () => {
    if (isLoadingHint || gameStatus !== "playing" || !sessionId) return;

    setIsLoadingHint(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/hint`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ sessionId }),
      });

      if (!response.ok) throw new Error("Failed to get hint");

      const data = await response.json();
      setHint(data.hint);
    } catch (error) {
      console.error("Error getting hint:", error);
      setHint("Try thinking of words related to common everyday concepts.");
    } finally {
      setIsLoadingHint(false);
    }
  };

  return (
    <div className="App">
      <Header
        onNewGame={startNewGame}
        gameStatus={gameStatus}
        attemptCount={guesses.length}
      />
      <GameBoard
        guesses={guesses}
        currentGuess={currentGuess}
        setCurrentGuess={setCurrentGuess}
        onGuess={addGuess}
        isLoading={isLoading}
        gameStatus={gameStatus}
        secretWord={secretWord}
        hint={hint}
        isLoadingHint={isLoadingHint}
        onGetHint={handleGetHint}
        mostRecentGuess={mostRecentGuess}
      />
    </div>
  );
}

export default App;
